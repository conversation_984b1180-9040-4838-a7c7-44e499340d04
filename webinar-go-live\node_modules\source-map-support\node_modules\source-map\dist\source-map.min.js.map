{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///source-map.min.js", "webpack:///webpack/bootstrap 0fd5815da764db5fb9fe", "webpack:///./source-map.js", "webpack:///./lib/source-map-generator.js", "webpack:///./lib/base64-vlq.js", "webpack:///./lib/base64.js", "webpack:///./lib/util.js", "webpack:///./lib/array-set.js", "webpack:///./lib/mapping-list.js", "webpack:///./lib/source-map-consumer.js", "webpack:///./lib/binary-search.js", "webpack:///./lib/quick-sort.js", "webpack:///./lib/source-node.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "modules", "__webpack_require__", "moduleId", "installedModules", "id", "loaded", "call", "m", "c", "p", "SourceMapGenerator", "SourceMapConsumer", "SourceNode", "aArgs", "_file", "util", "getArg", "_sourceRoot", "_skipValidation", "_sources", "ArraySet", "_names", "_mappings", "MappingList", "_sourcesContents", "base64VLQ", "prototype", "_version", "fromSourceMap", "aSourceMapConsumer", "sourceRoot", "generator", "file", "eachMapping", "mapping", "newMapping", "generated", "line", "generatedLine", "column", "generatedColumn", "source", "relative", "original", "originalLine", "originalColumn", "name", "addMapping", "sources", "for<PERSON>ach", "sourceFile", "sourceRelative", "has", "add", "content", "sourceContentFor", "setSourceContent", "_validateMapping", "String", "aSourceFile", "aSourceContent", "Object", "create", "toSetString", "keys", "length", "applySourceMap", "aSourceMapPath", "Error", "newSources", "newNames", "unsortedForEach", "originalPositionFor", "join", "aGenerated", "aOriginal", "aSource", "aName", "JSON", "stringify", "_serializeMappings", "next", "nameIdx", "sourceIdx", "previousGeneratedColumn", "previousGeneratedLine", "previousOriginalColumn", "previousOriginalLine", "previousName", "previousSource", "result", "mappings", "toArray", "i", "len", "compareByGeneratedPositionsInflated", "encode", "indexOf", "_generateSourcesContent", "aSources", "aSourceRoot", "map", "key", "hasOwnProperty", "toJSON", "version", "names", "sourcesContent", "toString", "toVLQSigned", "aValue", "fromVLQSigned", "isNegative", "shifted", "base64", "VLQ_BASE_SHIFT", "VLQ_BASE", "VLQ_BASE_MASK", "VLQ_CONTINUATION_BIT", "digit", "encoded", "vlq", "decode", "aStr", "aIndex", "aOutParam", "continuation", "strLen", "shift", "charCodeAt", "char<PERSON>t", "value", "rest", "intToCharMap", "split", "number", "TypeError", "charCode", "bigA", "bigZ", "littleA", "littleZ", "zero", "nine", "plus", "slash", "littleOffset", "numberOffset", "aDefaultValue", "arguments", "urlParse", "aUrl", "match", "urlRegexp", "scheme", "auth", "host", "port", "path", "urlGenerate", "aParsedUrl", "url", "normalize", "a<PERSON><PERSON>", "part", "isAbsolute", "parts", "up", "splice", "aRoot", "aPathUrl", "aRootUrl", "dataUrlRegexp", "joined", "replace", "level", "index", "lastIndexOf", "slice", "Array", "substr", "identity", "s", "isProtoString", "fromSetString", "compareByOriginalPositions", "mappingA", "mappingB", "onlyCompareOriginal", "cmp", "strcmp", "compareByGeneratedPositionsDeflated", "onlyCompareGenerated", "aStr1", "aStr2", "parseSourceMapInput", "str", "parse", "computeSourceURL", "sourceURL", "sourceMapURL", "parsed", "substring", "test", "supportsNullProto", "obj", "_array", "_set", "hasNativeMap", "Map", "fromArray", "aArray", "aAllowDuplicates", "set", "size", "getOwnPropertyNames", "sStr", "isDuplicate", "idx", "push", "get", "at", "aIdx", "generatedPositionAfter", "lineA", "lineB", "columnA", "columnB", "_sorted", "_last", "aCallback", "aThisArg", "aMapping", "sort", "aSourceMap", "aSourceMapURL", "sourceMap", "sections", "IndexedSourceMapConsumer", "BasicSourceMapConsumer", "_absoluteSources", "_sourceMapURL", "Mapping", "lastOffset", "_sections", "offset", "offsetLine", "offsetColumn", "generatedOffset", "consumer", "binarySearch", "quickSort", "__generatedMappings", "defineProperty", "configurable", "enumerable", "_parseMappings", "__originalMappings", "_charIsMappingSeparator", "GENERATED_ORDER", "ORIGINAL_ORDER", "GREATEST_LOWER_BOUND", "LEAST_UPPER_BOUND", "aContext", "aOrder", "context", "order", "_generatedMappings", "_originalMappings", "allGeneratedPositionsFor", "needle", "_findSourceIndex", "_findMapping", "undefined", "lastColumn", "relativeSource", "smc", "generatedMappings", "destGeneratedMappings", "destOriginalMappings", "srcMapping", "destMapping", "segment", "end", "cachedSegments", "temp", "originalMappings", "aNeedle", "aMappings", "aLineName", "aColumnName", "aComparator", "aBias", "search", "computeColumnSpans", "nextMapping", "lastGeneratedColumn", "Infinity", "hasContentsOfAllSources", "some", "sc", "nullOnMissing", "fileUriAbsPath", "generatedPositionFor", "constructor", "j", "sectionIndex", "section", "bias", "every", "generatedPosition", "ret", "sectionMappings", "adjustedMapping", "recursiveSearch", "aLow", "aHigh", "aHaystack", "aCompare", "mid", "Math", "floor", "swap", "ary", "x", "y", "randomIntInRange", "low", "high", "round", "random", "doQuickSort", "comparator", "r", "pivotIndex", "pivot", "q", "aLine", "aColumn", "aChunks", "children", "sourceContents", "isSourceNode", "REGEX_NEWLINE", "NEWLINE_CODE", "fromStringWithSourceMap", "aGeneratedCode", "aRelativePath", "addMappingWithCode", "code", "node", "remainingLines", "remainingLinesIndex", "shiftNextLine", "getNextLine", "lineContents", "newLine", "lastGeneratedLine", "lastMapping", "nextLine", "aChunk", "isArray", "chunk", "prepend", "unshift", "walk", "aFn", "aSep", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replaceRight", "aPattern", "aReplacement", "<PERSON><PERSON><PERSON><PERSON>", "walkSourceContents", "toStringWithSourceMap", "sourceMappingActive", "lastOriginalSource", "lastOriginalLine", "lastOriginalColumn", "lastOriginalName", "sourceContent"], "mappings": "CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,IACA,kBAAAG,gBAAAC,IACAD,UAAAH,GACA,gBAAAC,SACAA,QAAA,UAAAD,IAEAD,EAAA,UAAAC,KACCK,KAAA,WACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAP,OAGA,IAAAC,GAAAO,EAAAD,IACAP,WACAS,GAAAF,EACAG,QAAA,EAUA,OANAL,GAAAE,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAS,QAAA,EAGAT,EAAAD,QAvBA,GAAAQ,KAqCA,OATAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAQ,EAAA,GAGAR,EAAA,KDgBM,SAAUL,EAAQD,EAASM,GEjDjCN,EAAAe,mBAAAT,EAAA,GAAAS,mBACAf,EAAAgB,kBAAAV,EAAA,GAAAU,kBACAhB,EAAAiB,WAAAX,EAAA,IAAAW,YF6DM,SAAUhB,EAAQD,EAASM,GGhDjC,QAAAS,GAAAG,GACAA,IACAA,MAEAd,KAAAe,MAAAC,EAAAC,OAAAH,EAAA,aACAd,KAAAkB,YAAAF,EAAAC,OAAAH,EAAA,mBACAd,KAAAmB,gBAAAH,EAAAC,OAAAH,EAAA,qBACAd,KAAAoB,SAAA,GAAAC,GACArB,KAAAsB,OAAA,GAAAD,GACArB,KAAAuB,UAAA,GAAAC,GACAxB,KAAAyB,iBAAA,KAvBA,GAAAC,GAAAxB,EAAA,GACAc,EAAAd,EAAA,GACAmB,EAAAnB,EAAA,GAAAmB,SACAG,EAAAtB,EAAA,GAAAsB,WAuBAb,GAAAgB,UAAAC,SAAA,EAOAjB,EAAAkB,cACA,SAAAC,GACA,GAAAC,GAAAD,EAAAC,WACAC,EAAA,GAAArB,IACAsB,KAAAH,EAAAG,KACAF,cA2CA,OAzCAD,GAAAI,YAAA,SAAAC,GACA,GAAAC,IACAC,WACAC,KAAAH,EAAAI,cACAC,OAAAL,EAAAM,iBAIA,OAAAN,EAAAO,SACAN,EAAAM,OAAAP,EAAAO,OACA,MAAAX,IACAK,EAAAM,OAAA1B,EAAA2B,SAAAZ,EAAAK,EAAAM,SAGAN,EAAAQ,UACAN,KAAAH,EAAAU,aACAL,OAAAL,EAAAW,gBAGA,MAAAX,EAAAY,OACAX,EAAAW,KAAAZ,EAAAY,OAIAf,EAAAgB,WAAAZ,KAEAN,EAAAmB,QAAAC,QAAA,SAAAC,GACA,GAAAC,GAAAD,CACA,QAAApB,IACAqB,EAAApC,EAAA2B,SAAAZ,EAAAoB,IAGAnB,EAAAZ,SAAAiC,IAAAD,IACApB,EAAAZ,SAAAkC,IAAAF,EAGA,IAAAG,GAAAzB,EAAA0B,iBAAAL,EACA,OAAAI,GACAvB,EAAAyB,iBAAAN,EAAAI,KAGAvB,GAaArB,EAAAgB,UAAAqB,WACA,SAAAlC,GACA,GAAAuB,GAAArB,EAAAC,OAAAH,EAAA,aACA8B,EAAA5B,EAAAC,OAAAH,EAAA,iBACA4B,EAAA1B,EAAAC,OAAAH,EAAA,eACAiC,EAAA/B,EAAAC,OAAAH,EAAA,YAEAd,MAAAmB,iBACAnB,KAAA0D,iBAAArB,EAAAO,EAAAF,EAAAK,GAGA,MAAAL,IACAA,EAAAiB,OAAAjB,GACA1C,KAAAoB,SAAAiC,IAAAX,IACA1C,KAAAoB,SAAAkC,IAAAZ,IAIA,MAAAK,IACAA,EAAAY,OAAAZ,GACA/C,KAAAsB,OAAA+B,IAAAN,IACA/C,KAAAsB,OAAAgC,IAAAP,IAIA/C,KAAAuB,UAAA+B,KACAf,cAAAF,EAAAC,KACAG,gBAAAJ,EAAAG,OACAK,aAAA,MAAAD,KAAAN,KACAQ,eAAA,MAAAF,KAAAJ,OACAE,SACAK,UAOApC,EAAAgB,UAAA8B,iBACA,SAAAG,EAAAC,GACA,GAAAnB,GAAAkB,CACA,OAAA5D,KAAAkB,cACAwB,EAAA1B,EAAA2B,SAAA3C,KAAAkB,YAAAwB,IAGA,MAAAmB,GAGA7D,KAAAyB,mBACAzB,KAAAyB,iBAAAqC,OAAAC,OAAA,OAEA/D,KAAAyB,iBAAAT,EAAAgD,YAAAtB,IAAAmB,GACK7D,KAAAyB,yBAGLzB,MAAAyB,iBAAAT,EAAAgD,YAAAtB,IACA,IAAAoB,OAAAG,KAAAjE,KAAAyB,kBAAAyC,SACAlE,KAAAyB,iBAAA,QAqBAd,EAAAgB,UAAAwC,eACA,SAAArC,EAAA8B,EAAAQ,GACA,GAAAjB,GAAAS,CAEA,UAAAA,EAAA,CACA,SAAA9B,EAAAG,KACA,SAAAoC,OACA,gJAIAlB,GAAArB,EAAAG,KAEA,GAAAF,GAAA/B,KAAAkB,WAEA,OAAAa,IACAoB,EAAAnC,EAAA2B,SAAAZ,EAAAoB,GAIA,IAAAmB,GAAA,GAAAjD,GACAkD,EAAA,GAAAlD,EAGArB,MAAAuB,UAAAiD,gBAAA,SAAArC,GACA,GAAAA,EAAAO,SAAAS,GAAA,MAAAhB,EAAAU,aAAA,CAEA,GAAAD,GAAAd,EAAA2C,qBACAnC,KAAAH,EAAAU,aACAL,OAAAL,EAAAW,gBAEA,OAAAF,EAAAF,SAEAP,EAAAO,OAAAE,EAAAF,OACA,MAAA0B,IACAjC,EAAAO,OAAA1B,EAAA0D,KAAAN,EAAAjC,EAAAO,SAEA,MAAAX,IACAI,EAAAO,OAAA1B,EAAA2B,SAAAZ,EAAAI,EAAAO,SAEAP,EAAAU,aAAAD,EAAAN,KACAH,EAAAW,eAAAF,EAAAJ,OACA,MAAAI,EAAAG,OACAZ,EAAAY,KAAAH,EAAAG,OAKA,GAAAL,GAAAP,EAAAO,MACA,OAAAA,GAAA4B,EAAAjB,IAAAX,IACA4B,EAAAhB,IAAAZ,EAGA,IAAAK,GAAAZ,EAAAY,IACA,OAAAA,GAAAwB,EAAAlB,IAAAN,IACAwB,EAAAjB,IAAAP,IAGK/C,MACLA,KAAAoB,SAAAkD,EACAtE,KAAAsB,OAAAiD,EAGAzC,EAAAmB,QAAAC,QAAA,SAAAC,GACA,GAAAI,GAAAzB,EAAA0B,iBAAAL,EACA,OAAAI,IACA,MAAAa,IACAjB,EAAAnC,EAAA0D,KAAAN,EAAAjB,IAEA,MAAApB,IACAoB,EAAAnC,EAAA2B,SAAAZ,EAAAoB,IAEAnD,KAAAyD,iBAAAN,EAAAI,KAEKvD,OAcLW,EAAAgB,UAAA+B,iBACA,SAAAiB,EAAAC,EAAAC,EACAC,GAKA,GAAAF,GAAA,gBAAAA,GAAAtC,MAAA,gBAAAsC,GAAApC,OACA,SAAA6B,OACA,+OAMA,OAAAM,GAAA,QAAAA,IAAA,UAAAA,IACAA,EAAArC,KAAA,GAAAqC,EAAAnC,QAAA,IACAoC,GAAAC,GAAAC,MAIAH,GAAA,QAAAA,IAAA,UAAAA,IACAC,GAAA,QAAAA,IAAA,UAAAA,IACAD,EAAArC,KAAA,GAAAqC,EAAAnC,QAAA,GACAoC,EAAAtC,KAAA,GAAAsC,EAAApC,QAAA,GACAqC,GAKA,SAAAR,OAAA,oBAAAU,KAAAC,WACA3C,UAAAsC,EACAjC,OAAAmC,EACAjC,SAAAgC,EACA7B,KAAA+B,MASAnE,EAAAgB,UAAAsD,mBACA,WAcA,OANAC,GACA/C,EACAgD,EACAC,EAVAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,GAMAC,EAAA5F,KAAAuB,UAAAsE,UACAC,EAAA,EAAAC,EAAAH,EAAA1B,OAA0C4B,EAAAC,EAASD,IAAA,CAInD,GAHA3D,EAAAyD,EAAAE,GACAZ,EAAA,GAEA/C,EAAAI,gBAAA+C,EAEA,IADAD,EAAA,EACAlD,EAAAI,gBAAA+C,GACAJ,GAAA,IACAI,QAIA,IAAAQ,EAAA,GACA,IAAA9E,EAAAgF,oCAAA7D,EAAAyD,EAAAE,EAAA,IACA,QAEAZ,IAAA,IAIAA,GAAAxD,EAAAuE,OAAA9D,EAAAM,gBACA4C,GACAA,EAAAlD,EAAAM,gBAEA,MAAAN,EAAAO,SACA0C,EAAApF,KAAAoB,SAAA8E,QAAA/D,EAAAO,QACAwC,GAAAxD,EAAAuE,OAAAb,EAAAM,GACAA,EAAAN,EAGAF,GAAAxD,EAAAuE,OAAA9D,EAAAU,aAAA,EACA2C,GACAA,EAAArD,EAAAU,aAAA,EAEAqC,GAAAxD,EAAAuE,OAAA9D,EAAAW,eACAyC,GACAA,EAAApD,EAAAW,eAEA,MAAAX,EAAAY,OACAoC,EAAAnF,KAAAsB,OAAA4E,QAAA/D,EAAAY,MACAmC,GAAAxD,EAAAuE,OAAAd,EAAAM,GACAA,EAAAN,IAIAQ,GAAAT,EAGA,MAAAS,IAGAhF,EAAAgB,UAAAwE,wBACA,SAAAC,EAAAC,GACA,MAAAD,GAAAE,IAAA,SAAA5D,GACA,IAAA1C,KAAAyB,iBACA,WAEA,OAAA4E,IACA3D,EAAA1B,EAAA2B,SAAA0D,EAAA3D,GAEA,IAAA6D,GAAAvF,EAAAgD,YAAAtB,EACA,OAAAoB,QAAAnC,UAAA6E,eAAAjG,KAAAP,KAAAyB,iBAAA8E,GACAvG,KAAAyB,iBAAA8E,GACA,MACKvG,OAMLW,EAAAgB,UAAA8E,OACA,WACA,GAAAH,IACAI,QAAA1G,KAAA4B,SACAqB,QAAAjD,KAAAoB,SAAAyE,UACAc,MAAA3G,KAAAsB,OAAAuE,UACAD,SAAA5F,KAAAiF,qBAYA,OAVA,OAAAjF,KAAAe,QACAuF,EAAArE,KAAAjC,KAAAe,OAEA,MAAAf,KAAAkB,cACAoF,EAAAvE,WAAA/B,KAAAkB,aAEAlB,KAAAyB,mBACA6E,EAAAM,eAAA5G,KAAAmG,wBAAAG,EAAArD,QAAAqD,EAAAvE,aAGAuE,GAMA3F,EAAAgB,UAAAkF,SACA,WACA,MAAA9B,MAAAC,UAAAhF,KAAAyG,WAGA7G,EAAAe,sBH2EM,SAAUd,EAAQD,EAASM,GI/ajC,QAAA4G,GAAAC,GACA,MAAAA,GAAA,IACAA,GAAA,MACAA,GAAA,KASA,QAAAC,GAAAD,GACA,GAAAE,GAAA,OAAAF,GACAG,EAAAH,GAAA,CACA,OAAAE,IACAC,EACAA,EAhDA,GAAAC,GAAAjH,EAAA,GAcAkH,EAAA,EAGAC,EAAA,GAAAD,EAGAE,EAAAD,EAAA,EAGAE,EAAAF,CA+BAzH,GAAAqG,OAAA,SAAAc,GACA,GACAS,GADAC,EAAA,GAGAC,EAAAZ,EAAAC,EAEA,GACAS,GAAAE,EAAAJ,EACAI,KAAAN,EACAM,EAAA,IAGAF,GAAAD,GAEAE,GAAAN,EAAAlB,OAAAuB,SACGE,EAAA,EAEH,OAAAD,IAOA7H,EAAA+H,OAAA,SAAAC,EAAAC,EAAAC,GACA,GAGAC,GAAAP,EAHAQ,EAAAJ,EAAA1D,OACAyB,EAAA,EACAsC,EAAA,CAGA,IACA,GAAAJ,GAAAG,EACA,SAAA3D,OAAA,6CAIA,IADAmD,EAAAL,EAAAQ,OAAAC,EAAAM,WAAAL,MACAL,KAAA,EACA,SAAAnD,OAAA,yBAAAuD,EAAAO,OAAAN,EAAA,GAGAE,MAAAP,EAAAD,GACAC,GAAAF,EACA3B,GAAA6B,GAAAS,EACAA,GAAAb,QACGW,EAEHD,GAAAM,MAAApB,EAAArB,GACAmC,EAAAO,KAAAR,IJ2fM,SAAUhI,EAAQD,GK9nBxB,GAAA0I,GAAA,mEAAAC,MAAA,GAKA3I,GAAAqG,OAAA,SAAAuC,GACA,MAAAA,KAAAF,EAAApE,OACA,MAAAoE,GAAAE,EAEA,UAAAC,WAAA,6BAAAD,IAOA5I,EAAA+H,OAAA,SAAAe,GACA,GAAAC,GAAA,GACAC,EAAA,GAEAC,EAAA,GACAC,EAAA,IAEAC,EAAA,GACAC,EAAA,GAEAC,EAAA,GACAC,EAAA,GAEAC,EAAA,GACAC,EAAA,EAGA,OAAAT,IAAAD,MAAAE,EACAF,EAAAC,EAIAE,GAAAH,MAAAI,EACAJ,EAAAG,EAAAM,EAIAJ,GAAAL,MAAAM,EACAN,EAAAK,EAAAK,EAIAV,GAAAO,EACA,GAIAP,GAAAQ,EACA,IAIA,IL6oBM,SAAUrJ,EAAQD,GM7rBxB,QAAAqB,GAAAH,EAAAgE,EAAAuE,GACA,GAAAvE,IAAAhE,GACA,MAAAA,GAAAgE,EACG,QAAAwE,UAAApF,OACH,MAAAmF,EAEA,UAAAhF,OAAA,IAAAS,EAAA,6BAQA,QAAAyE,GAAAC,GACA,GAAAC,GAAAD,EAAAC,MAAAC,EACA,OAAAD,IAIAE,OAAAF,EAAA,GACAG,KAAAH,EAAA,GACAI,KAAAJ,EAAA,GACAK,KAAAL,EAAA,GACAM,KAAAN,EAAA,IAPA,KAYA,QAAAO,GAAAC,GACA,GAAAC,GAAA,EAiBA,OAhBAD,GAAAN,SACAO,GAAAD,EAAAN,OAAA,KAEAO,GAAA,KACAD,EAAAL,OACAM,GAAAD,EAAAL,KAAA,KAEAK,EAAAJ,OACAK,GAAAD,EAAAJ,MAEAI,EAAAH,OACAI,GAAA,IAAAD,EAAAH,MAEAG,EAAAF,OACAG,GAAAD,EAAAF,MAEAG,EAeA,QAAAC,GAAAC,GACA,GAAAL,GAAAK,EACAF,EAAAX,EAAAa,EACA,IAAAF,EAAA,CACA,IAAAA,EAAAH,KACA,MAAAK,EAEAL,GAAAG,EAAAH,KAKA,OAAAM,GAHAC,EAAA1K,EAAA0K,WAAAP,GAEAQ,EAAAR,EAAAxB,MAAA,OACAiC,EAAA,EAAA1E,EAAAyE,EAAArG,OAAA,EAA8C4B,GAAA,EAAQA,IACtDuE,EAAAE,EAAAzE,GACA,MAAAuE,EACAE,EAAAE,OAAA3E,EAAA,GACK,OAAAuE,EACLG,IACKA,EAAA,IACL,KAAAH,GAIAE,EAAAE,OAAA3E,EAAA,EAAA0E,GACAA,EAAA,IAEAD,EAAAE,OAAA3E,EAAA,GACA0E,KAUA,OANAT,GAAAQ,EAAA7F,KAAA,KAEA,KAAAqF,IACAA,EAAAO,EAAA,SAGAJ,GACAA,EAAAH,OACAC,EAAAE,IAEAH,EAoBA,QAAArF,GAAAgG,EAAAN,GACA,KAAAM,IACAA,EAAA,KAEA,KAAAN,IACAA,EAAA,IAEA,IAAAO,GAAApB,EAAAa,GACAQ,EAAArB,EAAAmB,EAMA,IALAE,IACAF,EAAAE,EAAAb,MAAA,KAIAY,MAAAhB,OAIA,MAHAiB,KACAD,EAAAhB,OAAAiB,EAAAjB,QAEAK,EAAAW,EAGA,IAAAA,GAAAP,EAAAX,MAAAoB,GACA,MAAAT,EAIA,IAAAQ,MAAAf,OAAAe,EAAAb,KAEA,MADAa,GAAAf,KAAAO,EACAJ,EAAAY,EAGA,IAAAE,GAAA,MAAAV,EAAAjC,OAAA,GACAiC,EACAD,EAAAO,EAAAK,QAAA,eAAAX,EAEA,OAAAQ,IACAA,EAAAb,KAAAe,EACAd,EAAAY,IAEAE,EAcA,QAAAnI,GAAA+H,EAAAN,GACA,KAAAM,IACAA,EAAA,KAGAA,IAAAK,QAAA,SAOA,KADA,GAAAC,GAAA,EACA,IAAAZ,EAAAlE,QAAAwE,EAAA,OACA,GAAAO,GAAAP,EAAAQ,YAAA,IACA,IAAAD,EAAA,EACA,MAAAb,EAOA,IADAM,IAAAS,MAAA,EAAAF,GACAP,EAAAjB,MAAA,qBACA,MAAAW,KAGAY,EAIA,MAAAI,OAAAJ,EAAA,GAAAtG,KAAA,OAAA0F,EAAAiB,OAAAX,EAAAxG,OAAA,GASA,QAAAoH,GAAAC,GACA,MAAAA,GAYA,QAAAvH,GAAA4D,GACA,MAAA4D,GAAA5D,GACA,IAAAA,EAGAA,EAIA,QAAA6D,GAAA7D,GACA,MAAA4D,GAAA5D,GACAA,EAAAuD,MAAA,GAGAvD,EAIA,QAAA4D,GAAAD,GACA,IAAAA,EACA,QAGA,IAAArH,GAAAqH,EAAArH,MAEA,IAAAA,EAAA,EACA,QAGA,SAAAqH,EAAArD,WAAAhE,EAAA,IACA,KAAAqH,EAAArD,WAAAhE,EAAA,IACA,MAAAqH,EAAArD,WAAAhE,EAAA,IACA,MAAAqH,EAAArD,WAAAhE,EAAA,IACA,MAAAqH,EAAArD,WAAAhE,EAAA,IACA,MAAAqH,EAAArD,WAAAhE,EAAA,IACA,MAAAqH,EAAArD,WAAAhE,EAAA,IACA,KAAAqH,EAAArD,WAAAhE,EAAA,IACA,KAAAqH,EAAArD,WAAAhE,EAAA,GACA,QAGA,QAAA4B,GAAA5B,EAAA,GAA2B4B,GAAA,EAAQA,IACnC,QAAAyF,EAAArD,WAAApC,GACA,QAIA,UAWA,QAAA4F,GAAAC,EAAAC,EAAAC,GACA,GAAAC,GAAAC,EAAAJ,EAAAjJ,OAAAkJ,EAAAlJ,OACA,YAAAoJ,EACAA,GAGAA,EAAAH,EAAA9I,aAAA+I,EAAA/I,aACA,IAAAiJ,EACAA,GAGAA,EAAAH,EAAA7I,eAAA8I,EAAA9I,eACA,IAAAgJ,GAAAD,EACAC,GAGAA,EAAAH,EAAAlJ,gBAAAmJ,EAAAnJ,gBACA,IAAAqJ,EACAA,GAGAA,EAAAH,EAAApJ,cAAAqJ,EAAArJ,cACA,IAAAuJ,EACAA,EAGAC,EAAAJ,EAAA5I,KAAA6I,EAAA7I,UAaA,QAAAiJ,GAAAL,EAAAC,EAAAK,GACA,GAAAH,GAAAH,EAAApJ,cAAAqJ,EAAArJ,aACA,YAAAuJ,EACAA,GAGAA,EAAAH,EAAAlJ,gBAAAmJ,EAAAnJ,gBACA,IAAAqJ,GAAAG,EACAH,GAGAA,EAAAC,EAAAJ,EAAAjJ,OAAAkJ,EAAAlJ,QACA,IAAAoJ,EACAA,GAGAA,EAAAH,EAAA9I,aAAA+I,EAAA/I,aACA,IAAAiJ,EACAA,GAGAA,EAAAH,EAAA7I,eAAA8I,EAAA9I,eACA,IAAAgJ,EACAA,EAGAC,EAAAJ,EAAA5I,KAAA6I,EAAA7I,UAIA,QAAAgJ,GAAAG,EAAAC,GACA,MAAAD,KAAAC,EACA,EAGA,OAAAD,EACA,EAGA,OAAAC,GACA,EAGAD,EAAAC,EACA,GAGA,EAOA,QAAAnG,GAAA2F,EAAAC,GACA,GAAAE,GAAAH,EAAApJ,cAAAqJ,EAAArJ,aACA,YAAAuJ,EACAA,GAGAA,EAAAH,EAAAlJ,gBAAAmJ,EAAAnJ,gBACA,IAAAqJ,EACAA,GAGAA,EAAAC,EAAAJ,EAAAjJ,OAAAkJ,EAAAlJ,QACA,IAAAoJ,EACAA,GAGAA,EAAAH,EAAA9I,aAAA+I,EAAA/I,aACA,IAAAiJ,EACAA,GAGAA,EAAAH,EAAA7I,eAAA8I,EAAA9I,eACA,IAAAgJ,EACAA,EAGAC,EAAAJ,EAAA5I,KAAA6I,EAAA7I,UASA,QAAAqJ,GAAAC,GACA,MAAAtH,MAAAuH,MAAAD,EAAAtB,QAAA,iBAAsC,KAQtC,QAAAwB,GAAAxK,EAAAyK,EAAAC,GA8BA,GA7BAD,KAAA,GAEAzK,IAEA,MAAAA,IAAAmC,OAAA,UAAAsI,EAAA,KACAzK,GAAA,KAOAyK,EAAAzK,EAAAyK,GAiBAC,EAAA,CACA,GAAAC,GAAAnD,EAAAkD,EACA,KAAAC,EACA,SAAArI,OAAA,mCAEA,IAAAqI,EAAA3C,KAAA,CAEA,GAAAkB,GAAAyB,EAAA3C,KAAAmB,YAAA,IACAD,IAAA,IACAyB,EAAA3C,KAAA2C,EAAA3C,KAAA4C,UAAA,EAAA1B,EAAA,IAGAuB,EAAA9H,EAAAsF,EAAA0C,GAAAF,GAGA,MAAArC,GAAAqC,GA3cA5M,EAAAqB,QAEA,IAAAyI,GAAA,iEACAmB,EAAA,eAeAjL,GAAA2J,WAsBA3J,EAAAoK,cAwDApK,EAAAuK,YA2DAvK,EAAA8E,OAEA9E,EAAA0K,WAAA,SAAAF,GACA,YAAAA,EAAAjC,OAAA,IAAAuB,EAAAkD,KAAAxC,IAyCAxK,EAAA+C,UAEA,IAAAkK,GAAA,WACA,GAAAC,GAAAhJ,OAAAC,OAAA,KACA,sBAAA+I,MAuBAlN,GAAAoE,YAAA6I,EAAAvB,EAAAtH,EASApE,EAAA6L,cAAAoB,EAAAvB,EAAAG,EAsEA7L,EAAA8L,6BAuCA9L,EAAAoM,sCAsDApM,EAAAoG,sCAUApG,EAAAwM,sBAqDAxM,EAAA2M,oBNqtBM,SAAU1M,EAAQD,EAASM,GO3qCjC,QAAAmB,KACArB,KAAA+M,UACA/M,KAAAgN,KAAAC,EAAA,GAAAC,KAAApJ,OAAAC,OAAA,MAZA,GAAA/C,GAAAd,EAAA,GACAmD,EAAAS,OAAAnC,UAAA6E,eACAyG,EAAA,mBAAAC,IAgBA7L,GAAA8L,UAAA,SAAAC,EAAAC,GAEA,OADAC,GAAA,GAAAjM,GACAyE,EAAA,EAAAC,EAAAqH,EAAAlJ,OAAsC4B,EAAAC,EAASD,IAC/CwH,EAAAhK,IAAA8J,EAAAtH,GAAAuH,EAEA,OAAAC,IASAjM,EAAAM,UAAA4L,KAAA,WACA,MAAAN,GAAAjN,KAAAgN,KAAAO,KAAAzJ,OAAA0J,oBAAAxN,KAAAgN,MAAA9I,QAQA7C,EAAAM,UAAA2B,IAAA,SAAAsE,EAAAyF,GACA,GAAAI,GAAAR,EAAArF,EAAA5G,EAAAgD,YAAA4D,GACA8F,EAAAT,EAAAjN,KAAAqD,IAAAuE,GAAAvE,EAAA9C,KAAAP,KAAAgN,KAAAS,GACAE,EAAA3N,KAAA+M,OAAA7I,MACAwJ,KAAAL,GACArN,KAAA+M,OAAAa,KAAAhG,GAEA8F,IACAT,EACAjN,KAAAgN,KAAAM,IAAA1F,EAAA+F,GAEA3N,KAAAgN,KAAAS,GAAAE,IAUAtM,EAAAM,UAAA0B,IAAA,SAAAuE,GACA,GAAAqF,EACA,MAAAjN,MAAAgN,KAAA3J,IAAAuE,EAEA,IAAA6F,GAAAzM,EAAAgD,YAAA4D,EACA,OAAAvE,GAAA9C,KAAAP,KAAAgN,KAAAS,IASApM,EAAAM,UAAAuE,QAAA,SAAA0B,GACA,GAAAqF,EAAA,CACA,GAAAU,GAAA3N,KAAAgN,KAAAa,IAAAjG,EACA,IAAA+F,GAAA,EACA,MAAAA,OAEG,CACH,GAAAF,GAAAzM,EAAAgD,YAAA4D,EACA,IAAAvE,EAAA9C,KAAAP,KAAAgN,KAAAS,GACA,MAAAzN,MAAAgN,KAAAS,GAIA,SAAApJ,OAAA,IAAAuD,EAAA,yBAQAvG,EAAAM,UAAAmM,GAAA,SAAAC,GACA,GAAAA,GAAA,GAAAA,EAAA/N,KAAA+M,OAAA7I,OACA,MAAAlE,MAAA+M,OAAAgB,EAEA,UAAA1J,OAAA,yBAAA0J,IAQA1M,EAAAM,UAAAkE,QAAA,WACA,MAAA7F,MAAA+M,OAAA5B,SAGAvL,EAAAyB,YPmsCM,SAAUxB,EAAQD,EAASM,GQ9yCjC,QAAA8N,GAAArC,EAAAC,GAEA,GAAAqC,GAAAtC,EAAApJ,cACA2L,EAAAtC,EAAArJ,cACA4L,EAAAxC,EAAAlJ,gBACA2L,EAAAxC,EAAAnJ,eACA,OAAAyL,GAAAD,GAAAC,GAAAD,GAAAG,GAAAD,GACAnN,EAAAgF,oCAAA2F,EAAAC,IAAA,EAQA,QAAApK,KACAxB,KAAA+M,UACA/M,KAAAqO,SAAA,EAEArO,KAAAsO,OAAgB/L,eAAA,EAAAE,gBAAA,GAzBhB,GAAAzB,GAAAd,EAAA,EAkCAsB,GAAAG,UAAA6C,gBACA,SAAA+J,EAAAC,GACAxO,KAAA+M,OAAA7J,QAAAqL,EAAAC,IAQAhN,EAAAG,UAAA2B,IAAA,SAAAmL,GACAT,EAAAhO,KAAAsO,MAAAG,IACAzO,KAAAsO,MAAAG,EACAzO,KAAA+M,OAAAa,KAAAa,KAEAzO,KAAAqO,SAAA,EACArO,KAAA+M,OAAAa,KAAAa,KAaAjN,EAAAG,UAAAkE,QAAA,WAKA,MAJA7F,MAAAqO,UACArO,KAAA+M,OAAA2B,KAAA1N,EAAAgF,qCACAhG,KAAAqO,SAAA,GAEArO,KAAA+M,QAGAnN,EAAA4B,eRk0CM,SAAU3B,EAAQD,EAASM,GSn4CjC,QAAAU,GAAA+N,EAAAC,GACA,GAAAC,GAAAF,CAKA,OAJA,gBAAAA,KACAE,EAAA7N,EAAAoL,oBAAAuC,IAGA,MAAAE,EAAAC,SACA,GAAAC,GAAAF,EAAAD,GACA,GAAAI,GAAAH,EAAAD,GA0QA,QAAAI,GAAAL,EAAAC,GACA,GAAAC,GAAAF,CACA,iBAAAA,KACAE,EAAA7N,EAAAoL,oBAAAuC,GAGA,IAAAjI,GAAA1F,EAAAC,OAAA4N,EAAA,WACA5L,EAAAjC,EAAAC,OAAA4N,EAAA,WAGAlI,EAAA3F,EAAAC,OAAA4N,EAAA,YACA9M,EAAAf,EAAAC,OAAA4N,EAAA,mBACAjI,EAAA5F,EAAAC,OAAA4N,EAAA,uBACAjJ,EAAA5E,EAAAC,OAAA4N,EAAA,YACA5M,EAAAjB,EAAAC,OAAA4N,EAAA,YAIA,IAAAnI,GAAA1G,KAAA4B,SACA,SAAAyC,OAAA,wBAAAqC,EAGA3E,KACAA,EAAAf,EAAAmJ,UAAApI,IAGAkB,IACAqD,IAAA3C,QAIA2C,IAAAtF,EAAAmJ,WAKA7D,IAAA,SAAA5D,GACA,MAAAX,IAAAf,EAAAsJ,WAAAvI,IAAAf,EAAAsJ,WAAA5H,GACA1B,EAAA2B,SAAAZ,EAAAW,GACAA,IAOA1C,KAAAsB,OAAAD,EAAA8L,UAAAxG,EAAAL,IAAA3C,SAAA,GACA3D,KAAAoB,SAAAC,EAAA8L,UAAAlK,GAAA,GAEAjD,KAAAiP,iBAAAjP,KAAAoB,SAAAyE,UAAAS,IAAA,SAAAiF,GACA,MAAAvK,GAAAuL,iBAAAxK,EAAAwJ,EAAAqD,KAGA5O,KAAA+B,aACA/B,KAAA4G,iBACA5G,KAAAuB,UAAAqE,EACA5F,KAAAkP,cAAAN,EACA5O,KAAAiC,OA4GA,QAAAkN,KACAnP,KAAAuC,cAAA,EACAvC,KAAAyC,gBAAA,EACAzC,KAAA0C,OAAA,KACA1C,KAAA6C,aAAA,KACA7C,KAAA8C,eAAA,KACA9C,KAAA+C,KAAA,KAkaA,QAAAgM,GAAAJ,EAAAC,GACA,GAAAC,GAAAF,CACA,iBAAAA,KACAE,EAAA7N,EAAAoL,oBAAAuC,GAGA,IAAAjI,GAAA1F,EAAAC,OAAA4N,EAAA,WACAC,EAAA9N,EAAAC,OAAA4N,EAAA,WAEA,IAAAnI,GAAA1G,KAAA4B,SACA,SAAAyC,OAAA,wBAAAqC,EAGA1G,MAAAoB,SAAA,GAAAC,GACArB,KAAAsB,OAAA,GAAAD,EAEA,IAAA+N,IACA9M,MAAA,EACAE,OAAA,EAEAxC,MAAAqP,UAAAP,EAAAxI,IAAA,SAAAiF,GACA,GAAAA,EAAArB,IAGA,SAAA7F,OAAA,qDAEA,IAAAiL,GAAAtO,EAAAC,OAAAsK,EAAA,UACAgE,EAAAvO,EAAAC,OAAAqO,EAAA,QACAE,EAAAxO,EAAAC,OAAAqO,EAAA,SAEA,IAAAC,EAAAH,EAAA9M,MACAiN,IAAAH,EAAA9M,MAAAkN,EAAAJ,EAAA5M,OACA,SAAA6B,OAAA,uDAIA,OAFA+K,GAAAE,GAGAG,iBAGAlN,cAAAgN,EAAA,EACA9M,gBAAA+M,EAAA,GAEAE,SAAA,GAAA9O,GAAAI,EAAAC,OAAAsK,EAAA,OAAAqD,MAh5BA,GAAA5N,GAAAd,EAAA,GACAyP,EAAAzP,EAAA,GACAmB,EAAAnB,EAAA,GAAAmB,SACAK,EAAAxB,EAAA,GACA0P,EAAA1P,EAAA,GAAA0P,SAaAhP,GAAAiB,cAAA,SAAA8M,EAAAC,GACA,MAAAI,GAAAnN,cAAA8M,EAAAC,IAMAhO,EAAAe,UAAAC,SAAA,EAgCAhB,EAAAe,UAAAkO,oBAAA,KACA/L,OAAAgM,eAAAlP,EAAAe,UAAA,sBACAoO,cAAA,EACAC,YAAA,EACAnC,IAAA,WAKA,MAJA7N,MAAA6P,qBACA7P,KAAAiQ,eAAAjQ,KAAAuB,UAAAvB,KAAA+B,YAGA/B,KAAA6P,uBAIAjP,EAAAe,UAAAuO,mBAAA,KACApM,OAAAgM,eAAAlP,EAAAe,UAAA,qBACAoO,cAAA,EACAC,YAAA,EACAnC,IAAA,WAKA,MAJA7N,MAAAkQ,oBACAlQ,KAAAiQ,eAAAjQ,KAAAuB,UAAAvB,KAAA+B,YAGA/B,KAAAkQ,sBAIAtP,EAAAe,UAAAwO,wBACA,SAAAvI,EAAAqD,GACA,GAAAxK,GAAAmH,EAAAO,OAAA8C,EACA,aAAAxK,GAAmB,MAAAA,GAQnBG,EAAAe,UAAAsO,eACA,SAAArI,EAAAvB,GACA,SAAAhC,OAAA,6CAGAzD,EAAAwP,gBAAA,EACAxP,EAAAyP,eAAA,EAEAzP,EAAA0P,qBAAA,EACA1P,EAAA2P,kBAAA,EAkBA3P,EAAAe,UAAAO,YACA,SAAAqM,EAAAiC,EAAAC,GACA,GAGA7K,GAHA8K,EAAAF,GAAA,KACAG,EAAAF,GAAA7P,EAAAwP,eAGA,QAAAO,GACA,IAAA/P,GAAAwP,gBACAxK,EAAA5F,KAAA4Q,kBACA,MACA,KAAAhQ,GAAAyP,eACAzK,EAAA5F,KAAA6Q,iBACA,MACA,SACA,SAAAxM,OAAA,+BAGA,GAAAtC,GAAA/B,KAAA+B,UACA6D,GAAAU,IAAA,SAAAnE,GACA,GAAAO,GAAA,OAAAP,EAAAO,OAAA,KAAA1C,KAAAoB,SAAA0M,GAAA3L,EAAAO,OAEA,OADAA,GAAA1B,EAAAuL,iBAAAxK,EAAAW,EAAA1C,KAAAkP,gBAEAxM,SACAH,cAAAJ,EAAAI,cACAE,gBAAAN,EAAAM,gBACAI,aAAAV,EAAAU,aACAC,eAAAX,EAAAW,eACAC,KAAA,OAAAZ,EAAAY,KAAA,KAAA/C,KAAAsB,OAAAwM,GAAA3L,EAAAY,QAEK/C,MAAAkD,QAAAqL,EAAAmC,IAyBL9P,EAAAe,UAAAmP,yBACA,SAAAhQ,GACA,GAAAwB,GAAAtB,EAAAC,OAAAH,EAAA,QAMAiQ,GACArO,OAAA1B,EAAAC,OAAAH,EAAA,UACA+B,aAAAP,EACAQ,eAAA9B,EAAAC,OAAAH,EAAA,YAIA,IADAiQ,EAAArO,OAAA1C,KAAAgR,iBAAAD,EAAArO,QACAqO,EAAArO,OAAA,EACA,QAGA,IAAAkD,MAEAqF,EAAAjL,KAAAiR,aAAAF,EACA/Q,KAAA6Q,kBACA,eACA,iBACA7P,EAAA0K,2BACAiE,EAAAY,kBACA,IAAAtF,GAAA,GACA,GAAA9I,GAAAnC,KAAA6Q,kBAAA5F,EAEA,IAAAiG,SAAApQ,EAAA0B,OAOA,IANA,GAAAK,GAAAV,EAAAU,aAMAV,KAAAU,kBACA+C,EAAAgI,MACAtL,KAAAtB,EAAAC,OAAAkB,EAAA,sBACAK,OAAAxB,EAAAC,OAAAkB,EAAA,wBACAgP,WAAAnQ,EAAAC,OAAAkB,EAAA,8BAGAA,EAAAnC,KAAA6Q,oBAAA5F,OASA,KANA,GAAAnI,GAAAX,EAAAW,eAMAX,GACAA,EAAAU,eAAAP,GACAH,EAAAW,mBACA8C,EAAAgI,MACAtL,KAAAtB,EAAAC,OAAAkB,EAAA,sBACAK,OAAAxB,EAAAC,OAAAkB,EAAA,wBACAgP,WAAAnQ,EAAAC,OAAAkB,EAAA,8BAGAA,EAAAnC,KAAA6Q,oBAAA5F,GAKA,MAAArF,IAGAhG,EAAAgB,oBAgGAoO,EAAArN,UAAAmC,OAAAC,OAAAnD,EAAAe,WACAqN,EAAArN,UAAA+N,SAAA9O,EAMAoO,EAAArN,UAAAqP,iBAAA,SAAAnM,GACA,GAAAuM,GAAAvM,CAKA,IAJA,MAAA7E,KAAA+B,aACAqP,EAAApQ,EAAA2B,SAAA3C,KAAA+B,WAAAqP,IAGApR,KAAAoB,SAAAiC,IAAA+N,GACA,MAAApR,MAAAoB,SAAA8E,QAAAkL,EAKA,IAAAtL,EACA,KAAAA,EAAA,EAAaA,EAAA9F,KAAAiP,iBAAA/K,SAAkC4B,EAC/C,GAAA9F,KAAAiP,iBAAAnJ,IAAAjB,EACA,MAAAiB,EAIA,WAYAkJ,EAAAnN,cACA,SAAA8M,EAAAC,GACA,GAAAyC,GAAAvN,OAAAC,OAAAiL,EAAArN,WAEAgF,EAAA0K,EAAA/P,OAAAD,EAAA8L,UAAAwB,EAAArN,OAAAuE,WAAA,GACA5C,EAAAoO,EAAAjQ,SAAAC,EAAA8L,UAAAwB,EAAAvN,SAAAyE,WAAA,EACAwL,GAAAtP,WAAA4M,EAAAzN,YACAmQ,EAAAzK,eAAA+H,EAAAxI,wBAAAkL,EAAAjQ,SAAAyE,UACAwL,EAAAtP,YACAsP,EAAApP,KAAA0M,EAAA5N,MACAsQ,EAAAnC,cAAAN,EACAyC,EAAApC,iBAAAoC,EAAAjQ,SAAAyE,UAAAS,IAAA,SAAAiF,GACA,MAAAvK,GAAAuL,iBAAA8E,EAAAtP,WAAAwJ,EAAAqD,IAYA,QAJA0C,GAAA3C,EAAApN,UAAAsE,UAAAsF,QACAoG,EAAAF,EAAAxB,uBACA2B,EAAAH,EAAAnB,sBAEApK,EAAA,EAAA5B,EAAAoN,EAAApN,OAAsD4B,EAAA5B,EAAY4B,IAAA,CAClE,GAAA2L,GAAAH,EAAAxL,GACA4L,EAAA,GAAAvC,EACAuC,GAAAnP,cAAAkP,EAAAlP,cACAmP,EAAAjP,gBAAAgP,EAAAhP,gBAEAgP,EAAA/O,SACAgP,EAAAhP,OAAAO,EAAAiD,QAAAuL,EAAA/O,QACAgP,EAAA7O,aAAA4O,EAAA5O,aACA6O,EAAA5O,eAAA2O,EAAA3O,eAEA2O,EAAA1O,OACA2O,EAAA3O,KAAA4D,EAAAT,QAAAuL,EAAA1O,OAGAyO,EAAA5D,KAAA8D,IAGAH,EAAA3D,KAAA8D,GAKA,MAFA9B,GAAAyB,EAAAnB,mBAAAlP,EAAA0K,4BAEA2F,GAMArC,EAAArN,UAAAC,SAAA,EAKAkC,OAAAgM,eAAAd,EAAArN,UAAA,WACAkM,IAAA,WACA,MAAA7N,MAAAiP,iBAAA9D,WAqBA6D,EAAArN,UAAAsO,eACA,SAAArI,EAAAvB,GAeA,IAdA,GAYAlE,GAAAkK,EAAAsF,EAAAC,EAAAxJ,EAZA7F,EAAA,EACA8C,EAAA,EACAG,EAAA,EACAD,EAAA,EACAG,EAAA,EACAD,EAAA,EACAvB,EAAA0D,EAAA1D,OACA+G,EAAA,EACA4G,KACAC,KACAC,KACAT,KAGArG,EAAA/G,GACA,SAAA0D,EAAAO,OAAA8C,GACA1I,IACA0I,IACA5F,EAAA,MAEA,UAAAuC,EAAAO,OAAA8C,GACAA,QAEA,CASA,IARA9I,EAAA,GAAAgN,GACAhN,EAAAI,gBAOAqP,EAAA3G,EAAyB2G,EAAA1N,IACzBlE,KAAAmQ,wBAAAvI,EAAAgK,GADuCA,KAQvC,GAHAvF,EAAAzE,EAAAuD,MAAAF,EAAA2G,GAEAD,EAAAE,EAAAxF,GAEApB,GAAAoB,EAAAnI,WACS,CAET,IADAyN,KACA1G,EAAA2G,GACAlQ,EAAAiG,OAAAC,EAAAqD,EAAA6G,GACA1J,EAAA0J,EAAA1J,MACA6C,EAAA6G,EAAAzJ,KACAsJ,EAAA/D,KAAAxF,EAGA,QAAAuJ,EAAAzN,OACA,SAAAG,OAAA,yCAGA,QAAAsN,EAAAzN,OACA,SAAAG,OAAA,yCAGAwN,GAAAxF,GAAAsF,EAIAxP,EAAAM,gBAAA4C,EAAAsM,EAAA,GACAtM,EAAAlD,EAAAM,gBAEAkP,EAAAzN,OAAA,IAEA/B,EAAAO,OAAAgD,EAAAiM,EAAA,GACAjM,GAAAiM,EAAA,GAGAxP,EAAAU,aAAA2C,EAAAmM,EAAA,GACAnM,EAAArD,EAAAU,aAEAV,EAAAU,cAAA,EAGAV,EAAAW,eAAAyC,EAAAoM,EAAA,GACApM,EAAApD,EAAAW,eAEA6O,EAAAzN,OAAA,IAEA/B,EAAAY,KAAA0C,EAAAkM,EAAA,GACAlM,GAAAkM,EAAA,KAIAL,EAAA1D,KAAAzL,GACA,gBAAAA,GAAAU,cACAkP,EAAAnE,KAAAzL,GAKAyN,EAAA0B,EAAAtQ,EAAAgL,qCACAhM,KAAA6P,oBAAAyB,EAEA1B,EAAAmC,EAAA/Q,EAAA0K,4BACA1L,KAAAkQ,mBAAA6B,GAOA/C,EAAArN,UAAAsP,aACA,SAAAe,EAAAC,EAAAC,EACAC,EAAAC,EAAAC,GAMA,GAAAL,EAAAE,IAAA,EACA,SAAAzJ,WAAA,gDACAuJ,EAAAE,GAEA,IAAAF,EAAAG,GAAA,EACA,SAAA1J,WAAA,kDACAuJ,EAAAG,GAGA,OAAAxC,GAAA2C,OAAAN,EAAAC,EAAAG,EAAAC,IAOArD,EAAArN,UAAA4Q,mBACA,WACA,OAAAtH,GAAA,EAAuBA,EAAAjL,KAAA4Q,mBAAA1M,SAAwC+G,EAAA,CAC/D,GAAA9I,GAAAnC,KAAA4Q,mBAAA3F,EAMA,IAAAA,EAAA,EAAAjL,KAAA4Q,mBAAA1M,OAAA,CACA,GAAAsO,GAAAxS,KAAA4Q,mBAAA3F,EAAA,EAEA,IAAA9I,EAAAI,gBAAAiQ,EAAAjQ,cAAA,CACAJ,EAAAsQ,oBAAAD,EAAA/P,gBAAA,CACA,WAKAN,EAAAsQ,oBAAAC,MA4BA1D,EAAArN,UAAA8C,oBACA,SAAA3D,GACA,GAAAiQ,IACAxO,cAAAvB,EAAAC,OAAAH,EAAA,QACA2B,gBAAAzB,EAAAC,OAAAH,EAAA,WAGAmK,EAAAjL,KAAAiR,aACAF,EACA/Q,KAAA4Q,mBACA,gBACA,kBACA5P,EAAAgL,oCACAhL,EAAAC,OAAAH,EAAA,OAAAF,EAAA0P,sBAGA,IAAArF,GAAA,GACA,GAAA9I,GAAAnC,KAAA4Q,mBAAA3F,EAEA,IAAA9I,EAAAI,gBAAAwO,EAAAxO,cAAA,CACA,GAAAG,GAAA1B,EAAAC,OAAAkB,EAAA,cACA,QAAAO,IACAA,EAAA1C,KAAAoB,SAAA0M,GAAApL,GACAA,EAAA1B,EAAAuL,iBAAAvM,KAAA+B,WAAAW,EAAA1C,KAAAkP,eAEA,IAAAnM,GAAA/B,EAAAC,OAAAkB,EAAA,YAIA,OAHA,QAAAY,IACAA,EAAA/C,KAAAsB,OAAAwM,GAAA/K,KAGAL,SACAJ,KAAAtB,EAAAC,OAAAkB,EAAA,qBACAK,OAAAxB,EAAAC,OAAAkB,EAAA,uBACAY,SAKA,OACAL,OAAA,KACAJ,KAAA,KACAE,OAAA,KACAO,KAAA,OAQAiM,EAAArN,UAAAgR,wBACA,WACA,QAAA3S,KAAA4G,iBAGA5G,KAAA4G,eAAA1C,QAAAlE,KAAAoB,SAAAmM,SACAvN,KAAA4G,eAAAgM,KAAA,SAAAC,GAA+C,aAAAA,MAQ/C7D,EAAArN,UAAA6B,iBACA,SAAAqB,EAAAiO,GACA,IAAA9S,KAAA4G,eACA,WAGA,IAAAqE,GAAAjL,KAAAgR,iBAAAnM,EACA,IAAAoG,GAAA,EACA,MAAAjL,MAAA4G,eAAAqE,EAGA,IAAAmG,GAAAvM,CACA,OAAA7E,KAAA+B,aACAqP,EAAApQ,EAAA2B,SAAA3C,KAAA+B,WAAAqP,GAGA,IAAAlH,EACA,UAAAlK,KAAA+B,aACAmI,EAAAlJ,EAAAuI,SAAAvJ,KAAA+B,aAAA,CAKA,GAAAgR,GAAA3B,EAAArG,QAAA,gBACA,YAAAb,EAAAP,QACA3J,KAAAoB,SAAAiC,IAAA0P,GACA,MAAA/S,MAAA4G,eAAA5G,KAAAoB,SAAA8E,QAAA6M,GAGA,MAAA7I,EAAAH,MAAA,KAAAG,EAAAH,OACA/J,KAAAoB,SAAAiC,IAAA,IAAA+N,GACA,MAAApR,MAAA4G,eAAA5G,KAAAoB,SAAA8E,QAAA,IAAAkL,IAQA,GAAA0B,EACA,WAGA,UAAAzO,OAAA,IAAA+M,EAAA,+BA2BApC,EAAArN,UAAAqR,qBACA,SAAAlS,GACA,GAAA4B,GAAA1B,EAAAC,OAAAH,EAAA,SAEA,IADA4B,EAAA1C,KAAAgR,iBAAAtO,GACAA,EAAA,EACA,OACAJ,KAAA,KACAE,OAAA,KACA2O,WAAA,KAIA,IAAAJ,IACArO,SACAG,aAAA7B,EAAAC,OAAAH,EAAA,QACAgC,eAAA9B,EAAAC,OAAAH,EAAA,WAGAmK,EAAAjL,KAAAiR,aACAF,EACA/Q,KAAA6Q,kBACA,eACA,iBACA7P,EAAA0K,2BACA1K,EAAAC,OAAAH,EAAA,OAAAF,EAAA0P,sBAGA,IAAArF,GAAA,GACA,GAAA9I,GAAAnC,KAAA6Q,kBAAA5F,EAEA,IAAA9I,EAAAO,SAAAqO,EAAArO,OACA,OACAJ,KAAAtB,EAAAC,OAAAkB,EAAA,sBACAK,OAAAxB,EAAAC,OAAAkB,EAAA,wBACAgP,WAAAnQ,EAAAC,OAAAkB,EAAA,6BAKA,OACAG,KAAA,KACAE,OAAA,KACA2O,WAAA,OAIAvR,EAAAoP,yBAmGAD,EAAApN,UAAAmC,OAAAC,OAAAnD,EAAAe,WACAoN,EAAApN,UAAAsR,YAAArS,EAKAmO,EAAApN,UAAAC,SAAA,EAKAkC,OAAAgM,eAAAf,EAAApN,UAAA,WACAkM,IAAA,WAEA,OADA5K,MACA6C,EAAA,EAAmBA,EAAA9F,KAAAqP,UAAAnL,OAA2B4B,IAC9C,OAAAoN,GAAA,EAAqBA,EAAAlT,KAAAqP,UAAAvJ,GAAA4J,SAAAzM,QAAAiB,OAA+CgP,IACpEjQ,EAAA2K,KAAA5N,KAAAqP,UAAAvJ,GAAA4J,SAAAzM,QAAAiQ,GAGA,OAAAjQ,MAuBA8L,EAAApN,UAAA8C,oBACA,SAAA3D,GACA,GAAAiQ,IACAxO,cAAAvB,EAAAC,OAAAH,EAAA,QACA2B,gBAAAzB,EAAAC,OAAAH,EAAA,WAKAqS,EAAAxD,EAAA2C,OAAAvB,EAAA/Q,KAAAqP,UACA,SAAA0B,EAAAqC,GACA,GAAAtH,GAAAiF,EAAAxO,cAAA6Q,EAAA3D,gBAAAlN,aACA,OAAAuJ,GACAA,EAGAiF,EAAAtO,gBACA2Q,EAAA3D,gBAAAhN,kBAEA2Q,EAAApT,KAAAqP,UAAA8D,EAEA,OAAAC,GASAA,EAAA1D,SAAAjL,qBACAnC,KAAAyO,EAAAxO,eACA6Q,EAAA3D,gBAAAlN,cAAA,GACAC,OAAAuO,EAAAtO,iBACA2Q,EAAA3D,gBAAAlN,gBAAAwO,EAAAxO,cACA6Q,EAAA3D,gBAAAhN,gBAAA,EACA,GACA4Q,KAAAvS,EAAAuS,QAdA3Q,OAAA,KACAJ,KAAA,KACAE,OAAA,KACAO,KAAA,OAmBAgM,EAAApN,UAAAgR,wBACA,WACA,MAAA3S,MAAAqP,UAAAiE,MAAA,SAAA/H,GACA,MAAAA,GAAAmE,SAAAiD,6BASA5D,EAAApN,UAAA6B,iBACA,SAAAqB,EAAAiO,GACA,OAAAhN,GAAA,EAAmBA,EAAA9F,KAAAqP,UAAAnL,OAA2B4B,IAAA,CAC9C,GAAAsN,GAAApT,KAAAqP,UAAAvJ,GAEAvC,EAAA6P,EAAA1D,SAAAlM,iBAAAqB,GAAA,EACA,IAAAtB,EACA,MAAAA,GAGA,GAAAuP,EACA,WAGA,UAAAzO,OAAA,IAAAQ,EAAA,+BAsBAkK,EAAApN,UAAAqR,qBACA,SAAAlS,GACA,OAAAgF,GAAA,EAAmBA,EAAA9F,KAAAqP,UAAAnL,OAA2B4B,IAAA,CAC9C,GAAAsN,GAAApT,KAAAqP,UAAAvJ,EAIA,IAAAsN,EAAA1D,SAAAsB,iBAAAhQ,EAAAC,OAAAH,EAAA,iBAGA,GAAAyS,GAAAH,EAAA1D,SAAAsD,qBAAAlS,EACA,IAAAyS,EAAA,CACA,GAAAC,IACAlR,KAAAiR,EAAAjR,MACA8Q,EAAA3D,gBAAAlN,cAAA,GACAC,OAAA+Q,EAAA/Q,QACA4Q,EAAA3D,gBAAAlN,gBAAAgR,EAAAjR,KACA8Q,EAAA3D,gBAAAhN,gBAAA,EACA,GAEA,OAAA+Q,KAIA,OACAlR,KAAA,KACAE,OAAA,OASAuM,EAAApN,UAAAsO,eACA,SAAArI,EAAAvB,GACArG,KAAA6P,uBACA7P,KAAAkQ,qBACA,QAAApK,GAAA,EAAmBA,EAAA9F,KAAAqP,UAAAnL,OAA2B4B,IAG9C,OAFAsN,GAAApT,KAAAqP,UAAAvJ,GACA2N,EAAAL,EAAA1D,SAAAkB,mBACAsC,EAAA,EAAqBA,EAAAO,EAAAvP,OAA4BgP,IAAA,CACjD,GAAA/Q,GAAAsR,EAAAP,GAEAxQ,EAAA0Q,EAAA1D,SAAAtO,SAAA0M,GAAA3L,EAAAO,OACAA,GAAA1B,EAAAuL,iBAAA6G,EAAA1D,SAAA3N,WAAAW,EAAA1C,KAAAkP,eACAlP,KAAAoB,SAAAkC,IAAAZ,GACAA,EAAA1C,KAAAoB,SAAA8E,QAAAxD,EAEA,IAAAK,GAAA,IACAZ,GAAAY,OACAA,EAAAqQ,EAAA1D,SAAApO,OAAAwM,GAAA3L,EAAAY,MACA/C,KAAAsB,OAAAgC,IAAAP,GACAA,EAAA/C,KAAAsB,OAAA4E,QAAAnD,GAOA,IAAA2Q,IACAhR,SACAH,cAAAJ,EAAAI,eACA6Q,EAAA3D,gBAAAlN,cAAA,GACAE,gBAAAN,EAAAM,iBACA2Q,EAAA3D,gBAAAlN,gBAAAJ,EAAAI,cACA6Q,EAAA3D,gBAAAhN,gBAAA,EACA,GACAI,aAAAV,EAAAU,aACAC,eAAAX,EAAAW,eACAC,OAGA/C,MAAA6P,oBAAAjC,KAAA8F,GACA,gBAAAA,GAAA7Q,cACA7C,KAAAkQ,mBAAAtC,KAAA8F,GAKA9D,EAAA5P,KAAA6P,oBAAA7O,EAAAgL,qCACA4D,EAAA5P,KAAAkQ,mBAAAlP,EAAA0K,6BAGA9L,EAAAmP,4BTu5CM,SAAUlP,EAAQD,GUx/ExB,QAAA+T,GAAAC,EAAAC,EAAA7B,EAAA8B,EAAAC,EAAA1B,GAUA,GAAA2B,GAAAC,KAAAC,OAAAL,EAAAD,GAAA,GAAAA,EACA9H,EAAAiI,EAAA/B,EAAA8B,EAAAE,IAAA,EACA,YAAAlI,EAEAkI,EAEAlI,EAAA,EAEA+H,EAAAG,EAAA,EAEAL,EAAAK,EAAAH,EAAA7B,EAAA8B,EAAAC,EAAA1B,GAKAA,GAAAzS,EAAA2Q,kBACAsD,EAAAC,EAAA5P,OAAA2P,GAAA,EAEAG,EAKAA,EAAAJ,EAAA,EAEAD,EAAAC,EAAAI,EAAAhC,EAAA8B,EAAAC,EAAA1B,GAIAA,GAAAzS,EAAA2Q,kBACAyD,EAEAJ,EAAA,KAAAA,EA1DAhU,EAAA0Q,qBAAA,EACA1Q,EAAA2Q,kBAAA,EAgFA3Q,EAAA0S,OAAA,SAAAN,EAAA8B,EAAAC,EAAA1B,GACA,OAAAyB,EAAA5P,OACA,QAGA,IAAA+G,GAAA0I,GAAA,EAAAG,EAAA5P,OAAA8N,EAAA8B,EACAC,EAAA1B,GAAAzS,EAAA0Q,qBACA,IAAArF,EAAA,EACA,QAMA,MAAAA,EAAA,MACA,IAAA8I,EAAAD,EAAA7I,GAAA6I,EAAA7I,EAAA,UAGAA,CAGA,OAAAA,KVuhFM,SAAUpL,EAAQD,GWzmFxB,QAAAuU,GAAAC,EAAAC,EAAAC,GACA,GAAAxC,GAAAsC,EAAAC,EACAD,GAAAC,GAAAD,EAAAE,GACAF,EAAAE,GAAAxC,EAWA,QAAAyC,GAAAC,EAAAC,GACA,MAAAR,MAAAS,MAAAF,EAAAP,KAAAU,UAAAF,EAAAD,IAeA,QAAAI,GAAAR,EAAAS,EAAAnU,EAAAoU,GAKA,GAAApU,EAAAoU,EAAA,CAYA,GAAAC,GAAAR,EAAA7T,EAAAoU,GACAhP,EAAApF,EAAA,CAEAyT,GAAAC,EAAAW,EAAAD,EASA,QARAE,GAAAZ,EAAAU,GAQA5B,EAAAxS,EAAmBwS,EAAA4B,EAAO5B,IAC1B2B,EAAAT,EAAAlB,GAAA8B,IAAA,IACAlP,GAAA,EACAqO,EAAAC,EAAAtO,EAAAoN,GAIAiB,GAAAC,EAAAtO,EAAA,EAAAoN,EACA,IAAA+B,GAAAnP,EAAA,CAIA8O,GAAAR,EAAAS,EAAAnU,EAAAuU,EAAA,GACAL,EAAAR,EAAAS,EAAAI,EAAA,EAAAH,IAYAlV,EAAAgQ,UAAA,SAAAwE,EAAAS,GACAD,EAAAR,EAAAS,EAAA,EAAAT,EAAAlQ,OAAA,KX4oFM,SAAUrE,EAAQD,EAASM,GY1tFjC,QAAAW,GAAAqU,EAAAC,EAAAtQ,EAAAuQ,EAAAtQ,GACA9E,KAAAqV,YACArV,KAAAsV,kBACAtV,KAAAsC,KAAA,MAAA4S,EAAA,KAAAA,EACAlV,KAAAwC,OAAA,MAAA2S,EAAA,KAAAA,EACAnV,KAAA0C,OAAA,MAAAmC,EAAA,KAAAA,EACA7E,KAAA+C,KAAA,MAAA+B,EAAA,KAAAA,EACA9E,KAAAuV,IAAA,EACA,MAAAH,GAAApV,KAAAsD,IAAA8R,GAnCA,GAAAzU,GAAAT,EAAA,GAAAS,mBACAK,EAAAd,EAAA,GAIAsV,EAAA,UAGAC,EAAA,GAKAF,EAAA,oBAiCA1U,GAAA6U,wBACA,SAAAC,EAAA7T,EAAA8T,GA+FA,QAAAC,GAAA1T,EAAA2T,GACA,UAAA3T,GAAA+O,SAAA/O,EAAAO,OACAqT,EAAAzS,IAAAwS,OACO,CACP,GAAApT,GAAAkT,EACA5U,EAAA0D,KAAAkR,EAAAzT,EAAAO,QACAP,EAAAO,MACAqT,GAAAzS,IAAA,GAAAzC,GAAAsB,EAAAU,aACAV,EAAAW,eACAJ,EACAoT,EACA3T,EAAAY,QAvGA,GAAAgT,GAAA,GAAAlV,GAMAmV,EAAAL,EAAApN,MAAAiN,GACAS,EAAA,EACAC,EAAA,WAMA,QAAAC,KACA,MAAAF,GAAAD,EAAA9R,OACA8R,EAAAC,KAAA/E,OAPA,GAAAkF,GAAAD,IAEAE,EAAAF,KAAA,EACA,OAAAC,GAAAC,GASAC,EAAA,EAAA7D,EAAA,EAKA8D,EAAA,IAgEA,OA9DAzU,GAAAI,YAAA,SAAAC,GACA,UAAAoU,EAAA,CAGA,KAAAD,EAAAnU,EAAAI,eAMS,CAIT,GAAAiU,GAAAR,EAAAC,IAAA,GACAH,EAAAU,EAAAnL,OAAA,EAAAlJ,EAAAM,gBACAgQ,EAOA,OANAuD,GAAAC,GAAAO,EAAAnL,OAAAlJ,EAAAM,gBACAgQ,GACAA,EAAAtQ,EAAAM,gBACAoT,EAAAU,EAAAT,QAEAS,EAAApU,GAhBA0T,EAAAU,EAAAL,KACAI,IACA7D,EAAA,EAqBA,KAAA6D,EAAAnU,EAAAI,eACAwT,EAAAzS,IAAA4S,KACAI,GAEA,IAAA7D,EAAAtQ,EAAAM,gBAAA,CACA,GAAA+T,GAAAR,EAAAC,IAAA,EACAF,GAAAzS,IAAAkT,EAAAnL,OAAA,EAAAlJ,EAAAM,kBACAuT,EAAAC,GAAAO,EAAAnL,OAAAlJ,EAAAM,iBACAgQ,EAAAtQ,EAAAM,gBAEA8T,EAAApU,GACKnC,MAELiW,EAAAD,EAAA9R,SACAqS,GAEAV,EAAAU,EAAAL,KAGAH,EAAAzS,IAAA0S,EAAAvL,OAAAwL,GAAAvR,KAAA,MAIA5C,EAAAmB,QAAAC,QAAA,SAAAC,GACA,GAAAI,GAAAzB,EAAA0B,iBAAAL,EACA,OAAAI,IACA,MAAAqS,IACAzS,EAAAnC,EAAA0D,KAAAkR,EAAAzS,IAEA4S,EAAAtS,iBAAAN,EAAAI,MAIAwS,GAwBAlV,EAAAc,UAAA2B,IAAA,SAAAmT,GACA,GAAArL,MAAAsL,QAAAD,GACAA,EAAAvT,QAAA,SAAAyT,GACA3W,KAAAsD,IAAAqT,IACK3W,UAEL,KAAAyW,EAAAlB,IAAA,gBAAAkB,GAMA,SAAAhO,WACA,8EAAAgO,EANAA,IACAzW,KAAAqV,SAAAzH,KAAA6I,GAQA,MAAAzW,OASAa,EAAAc,UAAAiV,QAAA,SAAAH,GACA,GAAArL,MAAAsL,QAAAD,GACA,OAAA3Q,GAAA2Q,EAAAvS,OAAA,EAAiC4B,GAAA,EAAQA,IACzC9F,KAAA4W,QAAAH,EAAA3Q,QAGA,KAAA2Q,EAAAlB,IAAA,gBAAAkB,GAIA,SAAAhO,WACA,8EAAAgO,EAJAzW,MAAAqV,SAAAwB,QAAAJ,GAOA,MAAAzW,OAUAa,EAAAc,UAAAmV,KAAA,SAAAC,GAEA,OADAJ,GACA7Q,EAAA,EAAAC,EAAA/F,KAAAqV,SAAAnR,OAA6C4B,EAAAC,EAASD,IACtD6Q,EAAA3W,KAAAqV,SAAAvP,GACA6Q,EAAApB,GACAoB,EAAAG,KAAAC,GAGA,KAAAJ,GACAI,EAAAJ,GAAoBjU,OAAA1C,KAAA0C,OACpBJ,KAAAtC,KAAAsC,KACAE,OAAAxC,KAAAwC,OACAO,KAAA/C,KAAA+C,QAYAlC,EAAAc,UAAA+C,KAAA,SAAAsS,GACA,GAAAC,GACAnR,EACAC,EAAA/F,KAAAqV,SAAAnR,MACA,IAAA6B,EAAA,GAEA,IADAkR,KACAnR,EAAA,EAAeA,EAAAC,EAAA,EAAWD,IAC1BmR,EAAArJ,KAAA5N,KAAAqV,SAAAvP,IACAmR,EAAArJ,KAAAoJ,EAEAC,GAAArJ,KAAA5N,KAAAqV,SAAAvP,IACA9F,KAAAqV,SAAA4B,EAEA,MAAAjX,OAUAa,EAAAc,UAAAuV,aAAA,SAAAC,EAAAC,GACA,GAAAC,GAAArX,KAAAqV,SAAArV,KAAAqV,SAAAnR,OAAA,EAUA,OATAmT,GAAA9B,GACA8B,EAAAH,aAAAC,EAAAC,GAEA,gBAAAC,GACArX,KAAAqV,SAAArV,KAAAqV,SAAAnR,OAAA,GAAAmT,EAAAtM,QAAAoM,EAAAC,GAGApX,KAAAqV,SAAAzH,KAAA,GAAA7C,QAAAoM,EAAAC,IAEApX,MAUAa,EAAAc,UAAA8B,iBACA,SAAAG,EAAAC,GACA7D,KAAAsV,eAAAtU,EAAAgD,YAAAJ,IAAAC,GASAhD,EAAAc,UAAA2V,mBACA,SAAAP,GACA,OAAAjR,GAAA,EAAAC,EAAA/F,KAAAqV,SAAAnR,OAA+C4B,EAAAC,EAASD,IACxD9F,KAAAqV,SAAAvP,GAAAyP,IACAvV,KAAAqV,SAAAvP,GAAAwR,mBAAAP,EAKA,QADA9T,GAAAa,OAAAG,KAAAjE,KAAAsV,gBACAxP,EAAA,EAAAC,EAAA9C,EAAAiB,OAAyC4B,EAAAC,EAASD,IAClDiR,EAAA/V,EAAAyK,cAAAxI,EAAA6C,IAAA9F,KAAAsV,eAAArS,EAAA6C,MAQAjF,EAAAc,UAAAkF,SAAA,WACA,GAAAwF,GAAA,EAIA,OAHArM,MAAA8W,KAAA,SAAAH,GACAtK,GAAAsK,IAEAtK,GAOAxL,EAAAc,UAAA4V,sBAAA,SAAAzW,GACA,GAAAuB,IACAyT,KAAA,GACAxT,KAAA,EACAE,OAAA,GAEA8D,EAAA,GAAA3F,GAAAG,GACA0W,GAAA,EACAC,EAAA,KACAC,EAAA,KACAC,EAAA,KACAC,EAAA,IAqEA,OApEA5X,MAAA8W,KAAA,SAAAH,EAAA/T,GACAP,EAAAyT,MAAAa,EACA,OAAA/T,EAAAF,QACA,OAAAE,EAAAN,MACA,OAAAM,EAAAJ,QACAiV,IAAA7U,EAAAF,QACAgV,IAAA9U,EAAAN,MACAqV,IAAA/U,EAAAJ,QACAoV,IAAAhV,EAAAG,MACAuD,EAAAtD,YACAN,OAAAE,EAAAF,OACAE,UACAN,KAAAM,EAAAN,KACAE,OAAAI,EAAAJ,QAEAH,WACAC,KAAAD,EAAAC,KACAE,OAAAH,EAAAG,QAEAO,KAAAH,EAAAG,OAGA0U,EAAA7U,EAAAF,OACAgV,EAAA9U,EAAAN,KACAqV,EAAA/U,EAAAJ,OACAoV,EAAAhV,EAAAG,KACAyU,GAAA,GACKA,IACLlR,EAAAtD,YACAX,WACAC,KAAAD,EAAAC,KACAE,OAAAH,EAAAG,UAGAiV,EAAA,KACAD,GAAA,EAEA,QAAA7J,GAAA,EAAAzJ,EAAAyS,EAAAzS,OAA4CyJ,EAAAzJ,EAAcyJ,IAC1DgJ,EAAAzO,WAAAyF,KAAA8H,GACApT,EAAAC,OACAD,EAAAG,OAAA,EAEAmL,EAAA,IAAAzJ,GACAuT,EAAA,KACAD,GAAA,GACSA,GACTlR,EAAAtD,YACAN,OAAAE,EAAAF,OACAE,UACAN,KAAAM,EAAAN,KACAE,OAAAI,EAAAJ,QAEAH,WACAC,KAAAD,EAAAC,KACAE,OAAAH,EAAAG,QAEAO,KAAAH,EAAAG,QAIAV,EAAAG,WAIAxC,KAAAsX,mBAAA,SAAAnU,EAAA0U,GACAvR,EAAA7C,iBAAAN,EAAA0U,MAGU/B,KAAAzT,EAAAyT,KAAAxP,QAGV1G,EAAAiB", "file": "source-map.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"sourceMap\"] = factory();\n\telse\n\t\troot[\"sourceMap\"] = factory();\n})(this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"sourceMap\"] = factory();\n\telse\n\t\troot[\"sourceMap\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId])\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\texports: {},\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*\n\t * Copyright 2009-2011 Mozilla Foundation and contributors\n\t * Licensed under the New BSD license. See LICENSE.txt or:\n\t * http://opensource.org/licenses/BSD-3-Clause\n\t */\n\texports.SourceMapGenerator = __webpack_require__(1).SourceMapGenerator;\n\texports.SourceMapConsumer = __webpack_require__(7).SourceMapConsumer;\n\texports.SourceNode = __webpack_require__(10).SourceNode;\n\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* -*- Mode: js; js-indent-level: 2; -*- */\n\t/*\n\t * Copyright 2011 Mozilla Foundation and contributors\n\t * Licensed under the New BSD license. See LICENSE or:\n\t * http://opensource.org/licenses/BSD-3-Clause\n\t */\n\t\n\tvar base64VLQ = __webpack_require__(2);\n\tvar util = __webpack_require__(4);\n\tvar ArraySet = __webpack_require__(5).ArraySet;\n\tvar MappingList = __webpack_require__(6).MappingList;\n\t\n\t/**\n\t * An instance of the SourceMapGenerator represents a source map which is\n\t * being built incrementally. You may pass an object with the following\n\t * properties:\n\t *\n\t *   - file: The filename of the generated source.\n\t *   - sourceRoot: A root for all relative URLs in this source map.\n\t */\n\tfunction SourceMapGenerator(aArgs) {\n\t  if (!aArgs) {\n\t    aArgs = {};\n\t  }\n\t  this._file = util.getArg(aArgs, 'file', null);\n\t  this._sourceRoot = util.getArg(aArgs, 'sourceRoot', null);\n\t  this._skipValidation = util.getArg(aArgs, 'skipValidation', false);\n\t  this._sources = new ArraySet();\n\t  this._names = new ArraySet();\n\t  this._mappings = new MappingList();\n\t  this._sourcesContents = null;\n\t}\n\t\n\tSourceMapGenerator.prototype._version = 3;\n\t\n\t/**\n\t * Creates a new SourceMapGenerator based on a SourceMapConsumer\n\t *\n\t * @param aSourceMapConsumer The SourceMap.\n\t */\n\tSourceMapGenerator.fromSourceMap =\n\t  function SourceMapGenerator_fromSourceMap(aSourceMapConsumer) {\n\t    var sourceRoot = aSourceMapConsumer.sourceRoot;\n\t    var generator = new SourceMapGenerator({\n\t      file: aSourceMapConsumer.file,\n\t      sourceRoot: sourceRoot\n\t    });\n\t    aSourceMapConsumer.eachMapping(function (mapping) {\n\t      var newMapping = {\n\t        generated: {\n\t          line: mapping.generatedLine,\n\t          column: mapping.generatedColumn\n\t        }\n\t      };\n\t\n\t      if (mapping.source != null) {\n\t        newMapping.source = mapping.source;\n\t        if (sourceRoot != null) {\n\t          newMapping.source = util.relative(sourceRoot, newMapping.source);\n\t        }\n\t\n\t        newMapping.original = {\n\t          line: mapping.originalLine,\n\t          column: mapping.originalColumn\n\t        };\n\t\n\t        if (mapping.name != null) {\n\t          newMapping.name = mapping.name;\n\t        }\n\t      }\n\t\n\t      generator.addMapping(newMapping);\n\t    });\n\t    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n\t      var sourceRelative = sourceFile;\n\t      if (sourceRoot !== null) {\n\t        sourceRelative = util.relative(sourceRoot, sourceFile);\n\t      }\n\t\n\t      if (!generator._sources.has(sourceRelative)) {\n\t        generator._sources.add(sourceRelative);\n\t      }\n\t\n\t      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n\t      if (content != null) {\n\t        generator.setSourceContent(sourceFile, content);\n\t      }\n\t    });\n\t    return generator;\n\t  };\n\t\n\t/**\n\t * Add a single mapping from original source line and column to the generated\n\t * source's line and column for this source map being created. The mapping\n\t * object should have the following properties:\n\t *\n\t *   - generated: An object with the generated line and column positions.\n\t *   - original: An object with the original line and column positions.\n\t *   - source: The original source file (relative to the sourceRoot).\n\t *   - name: An optional original token name for this mapping.\n\t */\n\tSourceMapGenerator.prototype.addMapping =\n\t  function SourceMapGenerator_addMapping(aArgs) {\n\t    var generated = util.getArg(aArgs, 'generated');\n\t    var original = util.getArg(aArgs, 'original', null);\n\t    var source = util.getArg(aArgs, 'source', null);\n\t    var name = util.getArg(aArgs, 'name', null);\n\t\n\t    if (!this._skipValidation) {\n\t      this._validateMapping(generated, original, source, name);\n\t    }\n\t\n\t    if (source != null) {\n\t      source = String(source);\n\t      if (!this._sources.has(source)) {\n\t        this._sources.add(source);\n\t      }\n\t    }\n\t\n\t    if (name != null) {\n\t      name = String(name);\n\t      if (!this._names.has(name)) {\n\t        this._names.add(name);\n\t      }\n\t    }\n\t\n\t    this._mappings.add({\n\t      generatedLine: generated.line,\n\t      generatedColumn: generated.column,\n\t      originalLine: original != null && original.line,\n\t      originalColumn: original != null && original.column,\n\t      source: source,\n\t      name: name\n\t    });\n\t  };\n\t\n\t/**\n\t * Set the source content for a source file.\n\t */\n\tSourceMapGenerator.prototype.setSourceContent =\n\t  function SourceMapGenerator_setSourceContent(aSourceFile, aSourceContent) {\n\t    var source = aSourceFile;\n\t    if (this._sourceRoot != null) {\n\t      source = util.relative(this._sourceRoot, source);\n\t    }\n\t\n\t    if (aSourceContent != null) {\n\t      // Add the source content to the _sourcesContents map.\n\t      // Create a new _sourcesContents map if the property is null.\n\t      if (!this._sourcesContents) {\n\t        this._sourcesContents = Object.create(null);\n\t      }\n\t      this._sourcesContents[util.toSetString(source)] = aSourceContent;\n\t    } else if (this._sourcesContents) {\n\t      // Remove the source file from the _sourcesContents map.\n\t      // If the _sourcesContents map is empty, set the property to null.\n\t      delete this._sourcesContents[util.toSetString(source)];\n\t      if (Object.keys(this._sourcesContents).length === 0) {\n\t        this._sourcesContents = null;\n\t      }\n\t    }\n\t  };\n\t\n\t/**\n\t * Applies the mappings of a sub-source-map for a specific source file to the\n\t * source map being generated. Each mapping to the supplied source file is\n\t * rewritten using the supplied source map. Note: The resolution for the\n\t * resulting mappings is the minimium of this map and the supplied map.\n\t *\n\t * @param aSourceMapConsumer The source map to be applied.\n\t * @param aSourceFile Optional. The filename of the source file.\n\t *        If omitted, SourceMapConsumer's file property will be used.\n\t * @param aSourceMapPath Optional. The dirname of the path to the source map\n\t *        to be applied. If relative, it is relative to the SourceMapConsumer.\n\t *        This parameter is needed when the two source maps aren't in the same\n\t *        directory, and the source map to be applied contains relative source\n\t *        paths. If so, those relative source paths need to be rewritten\n\t *        relative to the SourceMapGenerator.\n\t */\n\tSourceMapGenerator.prototype.applySourceMap =\n\t  function SourceMapGenerator_applySourceMap(aSourceMapConsumer, aSourceFile, aSourceMapPath) {\n\t    var sourceFile = aSourceFile;\n\t    // If aSourceFile is omitted, we will use the file property of the SourceMap\n\t    if (aSourceFile == null) {\n\t      if (aSourceMapConsumer.file == null) {\n\t        throw new Error(\n\t          'SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, ' +\n\t          'or the source map\\'s \"file\" property. Both were omitted.'\n\t        );\n\t      }\n\t      sourceFile = aSourceMapConsumer.file;\n\t    }\n\t    var sourceRoot = this._sourceRoot;\n\t    // Make \"sourceFile\" relative if an absolute Url is passed.\n\t    if (sourceRoot != null) {\n\t      sourceFile = util.relative(sourceRoot, sourceFile);\n\t    }\n\t    // Applying the SourceMap can add and remove items from the sources and\n\t    // the names array.\n\t    var newSources = new ArraySet();\n\t    var newNames = new ArraySet();\n\t\n\t    // Find mappings for the \"sourceFile\"\n\t    this._mappings.unsortedForEach(function (mapping) {\n\t      if (mapping.source === sourceFile && mapping.originalLine != null) {\n\t        // Check if it can be mapped by the source map, then update the mapping.\n\t        var original = aSourceMapConsumer.originalPositionFor({\n\t          line: mapping.originalLine,\n\t          column: mapping.originalColumn\n\t        });\n\t        if (original.source != null) {\n\t          // Copy mapping\n\t          mapping.source = original.source;\n\t          if (aSourceMapPath != null) {\n\t            mapping.source = util.join(aSourceMapPath, mapping.source)\n\t          }\n\t          if (sourceRoot != null) {\n\t            mapping.source = util.relative(sourceRoot, mapping.source);\n\t          }\n\t          mapping.originalLine = original.line;\n\t          mapping.originalColumn = original.column;\n\t          if (original.name != null) {\n\t            mapping.name = original.name;\n\t          }\n\t        }\n\t      }\n\t\n\t      var source = mapping.source;\n\t      if (source != null && !newSources.has(source)) {\n\t        newSources.add(source);\n\t      }\n\t\n\t      var name = mapping.name;\n\t      if (name != null && !newNames.has(name)) {\n\t        newNames.add(name);\n\t      }\n\t\n\t    }, this);\n\t    this._sources = newSources;\n\t    this._names = newNames;\n\t\n\t    // Copy sourcesContents of applied map.\n\t    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n\t      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n\t      if (content != null) {\n\t        if (aSourceMapPath != null) {\n\t          sourceFile = util.join(aSourceMapPath, sourceFile);\n\t        }\n\t        if (sourceRoot != null) {\n\t          sourceFile = util.relative(sourceRoot, sourceFile);\n\t        }\n\t        this.setSourceContent(sourceFile, content);\n\t      }\n\t    }, this);\n\t  };\n\t\n\t/**\n\t * A mapping can have one of the three levels of data:\n\t *\n\t *   1. Just the generated position.\n\t *   2. The Generated position, original position, and original source.\n\t *   3. Generated and original position, original source, as well as a name\n\t *      token.\n\t *\n\t * To maintain consistency, we validate that any new mapping being added falls\n\t * in to one of these categories.\n\t */\n\tSourceMapGenerator.prototype._validateMapping =\n\t  function SourceMapGenerator_validateMapping(aGenerated, aOriginal, aSource,\n\t                                              aName) {\n\t    // When aOriginal is truthy but has empty values for .line and .column,\n\t    // it is most likely a programmer error. In this case we throw a very\n\t    // specific error message to try to guide them the right way.\n\t    // For example: https://github.com/Polymer/polymer-bundler/pull/519\n\t    if (aOriginal && typeof aOriginal.line !== 'number' && typeof aOriginal.column !== 'number') {\n\t        throw new Error(\n\t            'original.line and original.column are not numbers -- you probably meant to omit ' +\n\t            'the original mapping entirely and only map the generated position. If so, pass ' +\n\t            'null for the original mapping instead of an object with empty or null values.'\n\t        );\n\t    }\n\t\n\t    if (aGenerated && 'line' in aGenerated && 'column' in aGenerated\n\t        && aGenerated.line > 0 && aGenerated.column >= 0\n\t        && !aOriginal && !aSource && !aName) {\n\t      // Case 1.\n\t      return;\n\t    }\n\t    else if (aGenerated && 'line' in aGenerated && 'column' in aGenerated\n\t             && aOriginal && 'line' in aOriginal && 'column' in aOriginal\n\t             && aGenerated.line > 0 && aGenerated.column >= 0\n\t             && aOriginal.line > 0 && aOriginal.column >= 0\n\t             && aSource) {\n\t      // Cases 2 and 3.\n\t      return;\n\t    }\n\t    else {\n\t      throw new Error('Invalid mapping: ' + JSON.stringify({\n\t        generated: aGenerated,\n\t        source: aSource,\n\t        original: aOriginal,\n\t        name: aName\n\t      }));\n\t    }\n\t  };\n\t\n\t/**\n\t * Serialize the accumulated mappings in to the stream of base 64 VLQs\n\t * specified by the source map format.\n\t */\n\tSourceMapGenerator.prototype._serializeMappings =\n\t  function SourceMapGenerator_serializeMappings() {\n\t    var previousGeneratedColumn = 0;\n\t    var previousGeneratedLine = 1;\n\t    var previousOriginalColumn = 0;\n\t    var previousOriginalLine = 0;\n\t    var previousName = 0;\n\t    var previousSource = 0;\n\t    var result = '';\n\t    var next;\n\t    var mapping;\n\t    var nameIdx;\n\t    var sourceIdx;\n\t\n\t    var mappings = this._mappings.toArray();\n\t    for (var i = 0, len = mappings.length; i < len; i++) {\n\t      mapping = mappings[i];\n\t      next = ''\n\t\n\t      if (mapping.generatedLine !== previousGeneratedLine) {\n\t        previousGeneratedColumn = 0;\n\t        while (mapping.generatedLine !== previousGeneratedLine) {\n\t          next += ';';\n\t          previousGeneratedLine++;\n\t        }\n\t      }\n\t      else {\n\t        if (i > 0) {\n\t          if (!util.compareByGeneratedPositionsInflated(mapping, mappings[i - 1])) {\n\t            continue;\n\t          }\n\t          next += ',';\n\t        }\n\t      }\n\t\n\t      next += base64VLQ.encode(mapping.generatedColumn\n\t                                 - previousGeneratedColumn);\n\t      previousGeneratedColumn = mapping.generatedColumn;\n\t\n\t      if (mapping.source != null) {\n\t        sourceIdx = this._sources.indexOf(mapping.source);\n\t        next += base64VLQ.encode(sourceIdx - previousSource);\n\t        previousSource = sourceIdx;\n\t\n\t        // lines are stored 0-based in SourceMap spec version 3\n\t        next += base64VLQ.encode(mapping.originalLine - 1\n\t                                   - previousOriginalLine);\n\t        previousOriginalLine = mapping.originalLine - 1;\n\t\n\t        next += base64VLQ.encode(mapping.originalColumn\n\t                                   - previousOriginalColumn);\n\t        previousOriginalColumn = mapping.originalColumn;\n\t\n\t        if (mapping.name != null) {\n\t          nameIdx = this._names.indexOf(mapping.name);\n\t          next += base64VLQ.encode(nameIdx - previousName);\n\t          previousName = nameIdx;\n\t        }\n\t      }\n\t\n\t      result += next;\n\t    }\n\t\n\t    return result;\n\t  };\n\t\n\tSourceMapGenerator.prototype._generateSourcesContent =\n\t  function SourceMapGenerator_generateSourcesContent(aSources, aSourceRoot) {\n\t    return aSources.map(function (source) {\n\t      if (!this._sourcesContents) {\n\t        return null;\n\t      }\n\t      if (aSourceRoot != null) {\n\t        source = util.relative(aSourceRoot, source);\n\t      }\n\t      var key = util.toSetString(source);\n\t      return Object.prototype.hasOwnProperty.call(this._sourcesContents, key)\n\t        ? this._sourcesContents[key]\n\t        : null;\n\t    }, this);\n\t  };\n\t\n\t/**\n\t * Externalize the source map.\n\t */\n\tSourceMapGenerator.prototype.toJSON =\n\t  function SourceMapGenerator_toJSON() {\n\t    var map = {\n\t      version: this._version,\n\t      sources: this._sources.toArray(),\n\t      names: this._names.toArray(),\n\t      mappings: this._serializeMappings()\n\t    };\n\t    if (this._file != null) {\n\t      map.file = this._file;\n\t    }\n\t    if (this._sourceRoot != null) {\n\t      map.sourceRoot = this._sourceRoot;\n\t    }\n\t    if (this._sourcesContents) {\n\t      map.sourcesContent = this._generateSourcesContent(map.sources, map.sourceRoot);\n\t    }\n\t\n\t    return map;\n\t  };\n\t\n\t/**\n\t * Render the source map being generated to a string.\n\t */\n\tSourceMapGenerator.prototype.toString =\n\t  function SourceMapGenerator_toString() {\n\t    return JSON.stringify(this.toJSON());\n\t  };\n\t\n\texports.SourceMapGenerator = SourceMapGenerator;\n\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* -*- Mode: js; js-indent-level: 2; -*- */\n\t/*\n\t * Copyright 2011 Mozilla Foundation and contributors\n\t * Licensed under the New BSD license. See LICENSE or:\n\t * http://opensource.org/licenses/BSD-3-Clause\n\t *\n\t * Based on the Base 64 VLQ implementation in Closure Compiler:\n\t * https://code.google.com/p/closure-compiler/source/browse/trunk/src/com/google/debugging/sourcemap/Base64VLQ.java\n\t *\n\t * Copyright 2011 The Closure Compiler Authors. All rights reserved.\n\t * Redistribution and use in source and binary forms, with or without\n\t * modification, are permitted provided that the following conditions are\n\t * met:\n\t *\n\t *  * Redistributions of source code must retain the above copyright\n\t *    notice, this list of conditions and the following disclaimer.\n\t *  * Redistributions in binary form must reproduce the above\n\t *    copyright notice, this list of conditions and the following\n\t *    disclaimer in the documentation and/or other materials provided\n\t *    with the distribution.\n\t *  * Neither the name of Google Inc. nor the names of its\n\t *    contributors may be used to endorse or promote products derived\n\t *    from this software without specific prior written permission.\n\t *\n\t * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n\t * \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n\t * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n\t * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n\t * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n\t * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n\t * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n\t * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n\t * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n\t * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n\t * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\t */\n\t\n\tvar base64 = __webpack_require__(3);\n\t\n\t// A single base 64 digit can contain 6 bits of data. For the base 64 variable\n\t// length quantities we use in the source map spec, the first bit is the sign,\n\t// the next four bits are the actual value, and the 6th bit is the\n\t// continuation bit. The continuation bit tells us whether there are more\n\t// digits in this value following this digit.\n\t//\n\t//   Continuation\n\t//   |    Sign\n\t//   |    |\n\t//   V    V\n\t//   101011\n\t\n\tvar VLQ_BASE_SHIFT = 5;\n\t\n\t// binary: 100000\n\tvar VLQ_BASE = 1 << VLQ_BASE_SHIFT;\n\t\n\t// binary: 011111\n\tvar VLQ_BASE_MASK = VLQ_BASE - 1;\n\t\n\t// binary: 100000\n\tvar VLQ_CONTINUATION_BIT = VLQ_BASE;\n\t\n\t/**\n\t * Converts from a two-complement value to a value where the sign bit is\n\t * placed in the least significant bit.  For example, as decimals:\n\t *   1 becomes 2 (10 binary), -1 becomes 3 (11 binary)\n\t *   2 becomes 4 (100 binary), -2 becomes 5 (101 binary)\n\t */\n\tfunction toVLQSigned(aValue) {\n\t  return aValue < 0\n\t    ? ((-aValue) << 1) + 1\n\t    : (aValue << 1) + 0;\n\t}\n\t\n\t/**\n\t * Converts to a two-complement value from a value where the sign bit is\n\t * placed in the least significant bit.  For example, as decimals:\n\t *   2 (10 binary) becomes 1, 3 (11 binary) becomes -1\n\t *   4 (100 binary) becomes 2, 5 (101 binary) becomes -2\n\t */\n\tfunction fromVLQSigned(aValue) {\n\t  var isNegative = (aValue & 1) === 1;\n\t  var shifted = aValue >> 1;\n\t  return isNegative\n\t    ? -shifted\n\t    : shifted;\n\t}\n\t\n\t/**\n\t * Returns the base 64 VLQ encoded value.\n\t */\n\texports.encode = function base64VLQ_encode(aValue) {\n\t  var encoded = \"\";\n\t  var digit;\n\t\n\t  var vlq = toVLQSigned(aValue);\n\t\n\t  do {\n\t    digit = vlq & VLQ_BASE_MASK;\n\t    vlq >>>= VLQ_BASE_SHIFT;\n\t    if (vlq > 0) {\n\t      // There are still more digits in this value, so we must make sure the\n\t      // continuation bit is marked.\n\t      digit |= VLQ_CONTINUATION_BIT;\n\t    }\n\t    encoded += base64.encode(digit);\n\t  } while (vlq > 0);\n\t\n\t  return encoded;\n\t};\n\t\n\t/**\n\t * Decodes the next base 64 VLQ value from the given string and returns the\n\t * value and the rest of the string via the out parameter.\n\t */\n\texports.decode = function base64VLQ_decode(aStr, aIndex, aOutParam) {\n\t  var strLen = aStr.length;\n\t  var result = 0;\n\t  var shift = 0;\n\t  var continuation, digit;\n\t\n\t  do {\n\t    if (aIndex >= strLen) {\n\t      throw new Error(\"Expected more digits in base 64 VLQ value.\");\n\t    }\n\t\n\t    digit = base64.decode(aStr.charCodeAt(aIndex++));\n\t    if (digit === -1) {\n\t      throw new Error(\"Invalid base64 digit: \" + aStr.charAt(aIndex - 1));\n\t    }\n\t\n\t    continuation = !!(digit & VLQ_CONTINUATION_BIT);\n\t    digit &= VLQ_BASE_MASK;\n\t    result = result + (digit << shift);\n\t    shift += VLQ_BASE_SHIFT;\n\t  } while (continuation);\n\t\n\t  aOutParam.value = fromVLQSigned(result);\n\t  aOutParam.rest = aIndex;\n\t};\n\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports) {\n\n\t/* -*- Mode: js; js-indent-level: 2; -*- */\n\t/*\n\t * Copyright 2011 Mozilla Foundation and contributors\n\t * Licensed under the New BSD license. See LICENSE or:\n\t * http://opensource.org/licenses/BSD-3-Clause\n\t */\n\t\n\tvar intToCharMap = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/'.split('');\n\t\n\t/**\n\t * Encode an integer in the range of 0 to 63 to a single base 64 digit.\n\t */\n\texports.encode = function (number) {\n\t  if (0 <= number && number < intToCharMap.length) {\n\t    return intToCharMap[number];\n\t  }\n\t  throw new TypeError(\"Must be between 0 and 63: \" + number);\n\t};\n\t\n\t/**\n\t * Decode a single base 64 character code digit to an integer. Returns -1 on\n\t * failure.\n\t */\n\texports.decode = function (charCode) {\n\t  var bigA = 65;     // 'A'\n\t  var bigZ = 90;     // 'Z'\n\t\n\t  var littleA = 97;  // 'a'\n\t  var littleZ = 122; // 'z'\n\t\n\t  var zero = 48;     // '0'\n\t  var nine = 57;     // '9'\n\t\n\t  var plus = 43;     // '+'\n\t  var slash = 47;    // '/'\n\t\n\t  var littleOffset = 26;\n\t  var numberOffset = 52;\n\t\n\t  // 0 - 25: ABCDEFGHIJKLMNOPQRSTUVWXYZ\n\t  if (bigA <= charCode && charCode <= bigZ) {\n\t    return (charCode - bigA);\n\t  }\n\t\n\t  // 26 - 51: abcdefghijklmnopqrstuvwxyz\n\t  if (littleA <= charCode && charCode <= littleZ) {\n\t    return (charCode - littleA + littleOffset);\n\t  }\n\t\n\t  // 52 - 61: **********\n\t  if (zero <= charCode && charCode <= nine) {\n\t    return (charCode - zero + numberOffset);\n\t  }\n\t\n\t  // 62: +\n\t  if (charCode == plus) {\n\t    return 62;\n\t  }\n\t\n\t  // 63: /\n\t  if (charCode == slash) {\n\t    return 63;\n\t  }\n\t\n\t  // Invalid base64 digit.\n\t  return -1;\n\t};\n\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports) {\n\n\t/* -*- Mode: js; js-indent-level: 2; -*- */\n\t/*\n\t * Copyright 2011 Mozilla Foundation and contributors\n\t * Licensed under the New BSD license. See LICENSE or:\n\t * http://opensource.org/licenses/BSD-3-Clause\n\t */\n\t\n\t/**\n\t * This is a helper function for getting values from parameter/options\n\t * objects.\n\t *\n\t * @param args The object we are extracting values from\n\t * @param name The name of the property we are getting.\n\t * @param defaultValue An optional value to return if the property is missing\n\t * from the object. If this is not specified and the property is missing, an\n\t * error will be thrown.\n\t */\n\tfunction getArg(aArgs, aName, aDefaultValue) {\n\t  if (aName in aArgs) {\n\t    return aArgs[aName];\n\t  } else if (arguments.length === 3) {\n\t    return aDefaultValue;\n\t  } else {\n\t    throw new Error('\"' + aName + '\" is a required argument.');\n\t  }\n\t}\n\texports.getArg = getArg;\n\t\n\tvar urlRegexp = /^(?:([\\w+\\-.]+):)?\\/\\/(?:(\\w+:\\w+)@)?([\\w.-]*)(?::(\\d+))?(.*)$/;\n\tvar dataUrlRegexp = /^data:.+\\,.+$/;\n\t\n\tfunction urlParse(aUrl) {\n\t  var match = aUrl.match(urlRegexp);\n\t  if (!match) {\n\t    return null;\n\t  }\n\t  return {\n\t    scheme: match[1],\n\t    auth: match[2],\n\t    host: match[3],\n\t    port: match[4],\n\t    path: match[5]\n\t  };\n\t}\n\texports.urlParse = urlParse;\n\t\n\tfunction urlGenerate(aParsedUrl) {\n\t  var url = '';\n\t  if (aParsedUrl.scheme) {\n\t    url += aParsedUrl.scheme + ':';\n\t  }\n\t  url += '//';\n\t  if (aParsedUrl.auth) {\n\t    url += aParsedUrl.auth + '@';\n\t  }\n\t  if (aParsedUrl.host) {\n\t    url += aParsedUrl.host;\n\t  }\n\t  if (aParsedUrl.port) {\n\t    url += \":\" + aParsedUrl.port\n\t  }\n\t  if (aParsedUrl.path) {\n\t    url += aParsedUrl.path;\n\t  }\n\t  return url;\n\t}\n\texports.urlGenerate = urlGenerate;\n\t\n\t/**\n\t * Normalizes a path, or the path portion of a URL:\n\t *\n\t * - Replaces consecutive slashes with one slash.\n\t * - Removes unnecessary '.' parts.\n\t * - Removes unnecessary '<dir>/..' parts.\n\t *\n\t * Based on code in the Node.js 'path' core module.\n\t *\n\t * @param aPath The path or url to normalize.\n\t */\n\tfunction normalize(aPath) {\n\t  var path = aPath;\n\t  var url = urlParse(aPath);\n\t  if (url) {\n\t    if (!url.path) {\n\t      return aPath;\n\t    }\n\t    path = url.path;\n\t  }\n\t  var isAbsolute = exports.isAbsolute(path);\n\t\n\t  var parts = path.split(/\\/+/);\n\t  for (var part, up = 0, i = parts.length - 1; i >= 0; i--) {\n\t    part = parts[i];\n\t    if (part === '.') {\n\t      parts.splice(i, 1);\n\t    } else if (part === '..') {\n\t      up++;\n\t    } else if (up > 0) {\n\t      if (part === '') {\n\t        // The first part is blank if the path is absolute. Trying to go\n\t        // above the root is a no-op. Therefore we can remove all '..' parts\n\t        // directly after the root.\n\t        parts.splice(i + 1, up);\n\t        up = 0;\n\t      } else {\n\t        parts.splice(i, 2);\n\t        up--;\n\t      }\n\t    }\n\t  }\n\t  path = parts.join('/');\n\t\n\t  if (path === '') {\n\t    path = isAbsolute ? '/' : '.';\n\t  }\n\t\n\t  if (url) {\n\t    url.path = path;\n\t    return urlGenerate(url);\n\t  }\n\t  return path;\n\t}\n\texports.normalize = normalize;\n\t\n\t/**\n\t * Joins two paths/URLs.\n\t *\n\t * @param aRoot The root path or URL.\n\t * @param aPath The path or URL to be joined with the root.\n\t *\n\t * - If aPath is a URL or a data URI, aPath is returned, unless aPath is a\n\t *   scheme-relative URL: Then the scheme of aRoot, if any, is prepended\n\t *   first.\n\t * - Otherwise aPath is a path. If aRoot is a URL, then its path portion\n\t *   is updated with the result and aRoot is returned. Otherwise the result\n\t *   is returned.\n\t *   - If aPath is absolute, the result is aPath.\n\t *   - Otherwise the two paths are joined with a slash.\n\t * - Joining for example 'http://' and 'www.example.com' is also supported.\n\t */\n\tfunction join(aRoot, aPath) {\n\t  if (aRoot === \"\") {\n\t    aRoot = \".\";\n\t  }\n\t  if (aPath === \"\") {\n\t    aPath = \".\";\n\t  }\n\t  var aPathUrl = urlParse(aPath);\n\t  var aRootUrl = urlParse(aRoot);\n\t  if (aRootUrl) {\n\t    aRoot = aRootUrl.path || '/';\n\t  }\n\t\n\t  // `join(foo, '//www.example.org')`\n\t  if (aPathUrl && !aPathUrl.scheme) {\n\t    if (aRootUrl) {\n\t      aPathUrl.scheme = aRootUrl.scheme;\n\t    }\n\t    return urlGenerate(aPathUrl);\n\t  }\n\t\n\t  if (aPathUrl || aPath.match(dataUrlRegexp)) {\n\t    return aPath;\n\t  }\n\t\n\t  // `join('http://', 'www.example.com')`\n\t  if (aRootUrl && !aRootUrl.host && !aRootUrl.path) {\n\t    aRootUrl.host = aPath;\n\t    return urlGenerate(aRootUrl);\n\t  }\n\t\n\t  var joined = aPath.charAt(0) === '/'\n\t    ? aPath\n\t    : normalize(aRoot.replace(/\\/+$/, '') + '/' + aPath);\n\t\n\t  if (aRootUrl) {\n\t    aRootUrl.path = joined;\n\t    return urlGenerate(aRootUrl);\n\t  }\n\t  return joined;\n\t}\n\texports.join = join;\n\t\n\texports.isAbsolute = function (aPath) {\n\t  return aPath.charAt(0) === '/' || urlRegexp.test(aPath);\n\t};\n\t\n\t/**\n\t * Make a path relative to a URL or another path.\n\t *\n\t * @param aRoot The root path or URL.\n\t * @param aPath The path or URL to be made relative to aRoot.\n\t */\n\tfunction relative(aRoot, aPath) {\n\t  if (aRoot === \"\") {\n\t    aRoot = \".\";\n\t  }\n\t\n\t  aRoot = aRoot.replace(/\\/$/, '');\n\t\n\t  // It is possible for the path to be above the root. In this case, simply\n\t  // checking whether the root is a prefix of the path won't work. Instead, we\n\t  // need to remove components from the root one by one, until either we find\n\t  // a prefix that fits, or we run out of components to remove.\n\t  var level = 0;\n\t  while (aPath.indexOf(aRoot + '/') !== 0) {\n\t    var index = aRoot.lastIndexOf(\"/\");\n\t    if (index < 0) {\n\t      return aPath;\n\t    }\n\t\n\t    // If the only part of the root that is left is the scheme (i.e. http://,\n\t    // file:///, etc.), one or more slashes (/), or simply nothing at all, we\n\t    // have exhausted all components, so the path is not relative to the root.\n\t    aRoot = aRoot.slice(0, index);\n\t    if (aRoot.match(/^([^\\/]+:\\/)?\\/*$/)) {\n\t      return aPath;\n\t    }\n\t\n\t    ++level;\n\t  }\n\t\n\t  // Make sure we add a \"../\" for each component we removed from the root.\n\t  return Array(level + 1).join(\"../\") + aPath.substr(aRoot.length + 1);\n\t}\n\texports.relative = relative;\n\t\n\tvar supportsNullProto = (function () {\n\t  var obj = Object.create(null);\n\t  return !('__proto__' in obj);\n\t}());\n\t\n\tfunction identity (s) {\n\t  return s;\n\t}\n\t\n\t/**\n\t * Because behavior goes wacky when you set `__proto__` on objects, we\n\t * have to prefix all the strings in our set with an arbitrary character.\n\t *\n\t * See https://github.com/mozilla/source-map/pull/31 and\n\t * https://github.com/mozilla/source-map/issues/30\n\t *\n\t * @param String aStr\n\t */\n\tfunction toSetString(aStr) {\n\t  if (isProtoString(aStr)) {\n\t    return '$' + aStr;\n\t  }\n\t\n\t  return aStr;\n\t}\n\texports.toSetString = supportsNullProto ? identity : toSetString;\n\t\n\tfunction fromSetString(aStr) {\n\t  if (isProtoString(aStr)) {\n\t    return aStr.slice(1);\n\t  }\n\t\n\t  return aStr;\n\t}\n\texports.fromSetString = supportsNullProto ? identity : fromSetString;\n\t\n\tfunction isProtoString(s) {\n\t  if (!s) {\n\t    return false;\n\t  }\n\t\n\t  var length = s.length;\n\t\n\t  if (length < 9 /* \"__proto__\".length */) {\n\t    return false;\n\t  }\n\t\n\t  if (s.charCodeAt(length - 1) !== 95  /* '_' */ ||\n\t      s.charCodeAt(length - 2) !== 95  /* '_' */ ||\n\t      s.charCodeAt(length - 3) !== 111 /* 'o' */ ||\n\t      s.charCodeAt(length - 4) !== 116 /* 't' */ ||\n\t      s.charCodeAt(length - 5) !== 111 /* 'o' */ ||\n\t      s.charCodeAt(length - 6) !== 114 /* 'r' */ ||\n\t      s.charCodeAt(length - 7) !== 112 /* 'p' */ ||\n\t      s.charCodeAt(length - 8) !== 95  /* '_' */ ||\n\t      s.charCodeAt(length - 9) !== 95  /* '_' */) {\n\t    return false;\n\t  }\n\t\n\t  for (var i = length - 10; i >= 0; i--) {\n\t    if (s.charCodeAt(i) !== 36 /* '$' */) {\n\t      return false;\n\t    }\n\t  }\n\t\n\t  return true;\n\t}\n\t\n\t/**\n\t * Comparator between two mappings where the original positions are compared.\n\t *\n\t * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n\t * mappings with the same original source/line/column, but different generated\n\t * line and column the same. Useful when searching for a mapping with a\n\t * stubbed out mapping.\n\t */\n\tfunction compareByOriginalPositions(mappingA, mappingB, onlyCompareOriginal) {\n\t  var cmp = strcmp(mappingA.source, mappingB.source);\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = mappingA.originalLine - mappingB.originalLine;\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = mappingA.originalColumn - mappingB.originalColumn;\n\t  if (cmp !== 0 || onlyCompareOriginal) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = mappingA.generatedLine - mappingB.generatedLine;\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  return strcmp(mappingA.name, mappingB.name);\n\t}\n\texports.compareByOriginalPositions = compareByOriginalPositions;\n\t\n\t/**\n\t * Comparator between two mappings with deflated source and name indices where\n\t * the generated positions are compared.\n\t *\n\t * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n\t * mappings with the same generated line and column, but different\n\t * source/name/original line and column the same. Useful when searching for a\n\t * mapping with a stubbed out mapping.\n\t */\n\tfunction compareByGeneratedPositionsDeflated(mappingA, mappingB, onlyCompareGenerated) {\n\t  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n\t  if (cmp !== 0 || onlyCompareGenerated) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = strcmp(mappingA.source, mappingB.source);\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = mappingA.originalLine - mappingB.originalLine;\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = mappingA.originalColumn - mappingB.originalColumn;\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  return strcmp(mappingA.name, mappingB.name);\n\t}\n\texports.compareByGeneratedPositionsDeflated = compareByGeneratedPositionsDeflated;\n\t\n\tfunction strcmp(aStr1, aStr2) {\n\t  if (aStr1 === aStr2) {\n\t    return 0;\n\t  }\n\t\n\t  if (aStr1 === null) {\n\t    return 1; // aStr2 !== null\n\t  }\n\t\n\t  if (aStr2 === null) {\n\t    return -1; // aStr1 !== null\n\t  }\n\t\n\t  if (aStr1 > aStr2) {\n\t    return 1;\n\t  }\n\t\n\t  return -1;\n\t}\n\t\n\t/**\n\t * Comparator between two mappings with inflated source and name strings where\n\t * the generated positions are compared.\n\t */\n\tfunction compareByGeneratedPositionsInflated(mappingA, mappingB) {\n\t  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = strcmp(mappingA.source, mappingB.source);\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = mappingA.originalLine - mappingB.originalLine;\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  cmp = mappingA.originalColumn - mappingB.originalColumn;\n\t  if (cmp !== 0) {\n\t    return cmp;\n\t  }\n\t\n\t  return strcmp(mappingA.name, mappingB.name);\n\t}\n\texports.compareByGeneratedPositionsInflated = compareByGeneratedPositionsInflated;\n\t\n\t/**\n\t * Strip any JSON XSSI avoidance prefix from the string (as documented\n\t * in the source maps specification), and then parse the string as\n\t * JSON.\n\t */\n\tfunction parseSourceMapInput(str) {\n\t  return JSON.parse(str.replace(/^\\)]}'[^\\n]*\\n/, ''));\n\t}\n\texports.parseSourceMapInput = parseSourceMapInput;\n\t\n\t/**\n\t * Compute the URL of a source given the the source root, the source's\n\t * URL, and the source map's URL.\n\t */\n\tfunction computeSourceURL(sourceRoot, sourceURL, sourceMapURL) {\n\t  sourceURL = sourceURL || '';\n\t\n\t  if (sourceRoot) {\n\t    // This follows what Chrome does.\n\t    if (sourceRoot[sourceRoot.length - 1] !== '/' && sourceURL[0] !== '/') {\n\t      sourceRoot += '/';\n\t    }\n\t    // The spec says:\n\t    //   Line 4: An optional source root, useful for relocating source\n\t    //   files on a server or removing repeated values in the\n\t    //   “sources” entry.  This value is prepended to the individual\n\t    //   entries in the “source” field.\n\t    sourceURL = sourceRoot + sourceURL;\n\t  }\n\t\n\t  // Historically, SourceMapConsumer did not take the sourceMapURL as\n\t  // a parameter.  This mode is still somewhat supported, which is why\n\t  // this code block is conditional.  However, it's preferable to pass\n\t  // the source map URL to SourceMapConsumer, so that this function\n\t  // can implement the source URL resolution algorithm as outlined in\n\t  // the spec.  This block is basically the equivalent of:\n\t  //    new URL(sourceURL, sourceMapURL).toString()\n\t  // ... except it avoids using URL, which wasn't available in the\n\t  // older releases of node still supported by this library.\n\t  //\n\t  // The spec says:\n\t  //   If the sources are not absolute URLs after prepending of the\n\t  //   “sourceRoot”, the sources are resolved relative to the\n\t  //   SourceMap (like resolving script src in a html document).\n\t  if (sourceMapURL) {\n\t    var parsed = urlParse(sourceMapURL);\n\t    if (!parsed) {\n\t      throw new Error(\"sourceMapURL could not be parsed\");\n\t    }\n\t    if (parsed.path) {\n\t      // Strip the last path component, but keep the \"/\".\n\t      var index = parsed.path.lastIndexOf('/');\n\t      if (index >= 0) {\n\t        parsed.path = parsed.path.substring(0, index + 1);\n\t      }\n\t    }\n\t    sourceURL = join(urlGenerate(parsed), sourceURL);\n\t  }\n\t\n\t  return normalize(sourceURL);\n\t}\n\texports.computeSourceURL = computeSourceURL;\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* -*- Mode: js; js-indent-level: 2; -*- */\n\t/*\n\t * Copyright 2011 Mozilla Foundation and contributors\n\t * Licensed under the New BSD license. See LICENSE or:\n\t * http://opensource.org/licenses/BSD-3-Clause\n\t */\n\t\n\tvar util = __webpack_require__(4);\n\tvar has = Object.prototype.hasOwnProperty;\n\tvar hasNativeMap = typeof Map !== \"undefined\";\n\t\n\t/**\n\t * A data structure which is a combination of an array and a set. Adding a new\n\t * member is O(1), testing for membership is O(1), and finding the index of an\n\t * element is O(1). Removing elements from the set is not supported. Only\n\t * strings are supported for membership.\n\t */\n\tfunction ArraySet() {\n\t  this._array = [];\n\t  this._set = hasNativeMap ? new Map() : Object.create(null);\n\t}\n\t\n\t/**\n\t * Static method for creating ArraySet instances from an existing array.\n\t */\n\tArraySet.fromArray = function ArraySet_fromArray(aArray, aAllowDuplicates) {\n\t  var set = new ArraySet();\n\t  for (var i = 0, len = aArray.length; i < len; i++) {\n\t    set.add(aArray[i], aAllowDuplicates);\n\t  }\n\t  return set;\n\t};\n\t\n\t/**\n\t * Return how many unique items are in this ArraySet. If duplicates have been\n\t * added, than those do not count towards the size.\n\t *\n\t * @returns Number\n\t */\n\tArraySet.prototype.size = function ArraySet_size() {\n\t  return hasNativeMap ? this._set.size : Object.getOwnPropertyNames(this._set).length;\n\t};\n\t\n\t/**\n\t * Add the given string to this set.\n\t *\n\t * @param String aStr\n\t */\n\tArraySet.prototype.add = function ArraySet_add(aStr, aAllowDuplicates) {\n\t  var sStr = hasNativeMap ? aStr : util.toSetString(aStr);\n\t  var isDuplicate = hasNativeMap ? this.has(aStr) : has.call(this._set, sStr);\n\t  var idx = this._array.length;\n\t  if (!isDuplicate || aAllowDuplicates) {\n\t    this._array.push(aStr);\n\t  }\n\t  if (!isDuplicate) {\n\t    if (hasNativeMap) {\n\t      this._set.set(aStr, idx);\n\t    } else {\n\t      this._set[sStr] = idx;\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Is the given string a member of this set?\n\t *\n\t * @param String aStr\n\t */\n\tArraySet.prototype.has = function ArraySet_has(aStr) {\n\t  if (hasNativeMap) {\n\t    return this._set.has(aStr);\n\t  } else {\n\t    var sStr = util.toSetString(aStr);\n\t    return has.call(this._set, sStr);\n\t  }\n\t};\n\t\n\t/**\n\t * What is the index of the given string in the array?\n\t *\n\t * @param String aStr\n\t */\n\tArraySet.prototype.indexOf = function ArraySet_indexOf(aStr) {\n\t  if (hasNativeMap) {\n\t    var idx = this._set.get(aStr);\n\t    if (idx >= 0) {\n\t        return idx;\n\t    }\n\t  } else {\n\t    var sStr = util.toSetString(aStr);\n\t    if (has.call(this._set, sStr)) {\n\t      return this._set[sStr];\n\t    }\n\t  }\n\t\n\t  throw new Error('\"' + aStr + '\" is not in the set.');\n\t};\n\t\n\t/**\n\t * What is the element at the given index?\n\t *\n\t * @param Number aIdx\n\t */\n\tArraySet.prototype.at = function ArraySet_at(aIdx) {\n\t  if (aIdx >= 0 && aIdx < this._array.length) {\n\t    return this._array[aIdx];\n\t  }\n\t  throw new Error('No element indexed by ' + aIdx);\n\t};\n\t\n\t/**\n\t * Returns the array representation of this set (which has the proper indices\n\t * indicated by indexOf). Note that this is a copy of the internal array used\n\t * for storing the members so that no one can mess with internal state.\n\t */\n\tArraySet.prototype.toArray = function ArraySet_toArray() {\n\t  return this._array.slice();\n\t};\n\t\n\texports.ArraySet = ArraySet;\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* -*- Mode: js; js-indent-level: 2; -*- */\n\t/*\n\t * Copyright 2014 Mozilla Foundation and contributors\n\t * Licensed under the New BSD license. See LICENSE or:\n\t * http://opensource.org/licenses/BSD-3-Clause\n\t */\n\t\n\tvar util = __webpack_require__(4);\n\t\n\t/**\n\t * Determine whether mappingB is after mappingA with respect to generated\n\t * position.\n\t */\n\tfunction generatedPositionAfter(mappingA, mappingB) {\n\t  // Optimized for most common case\n\t  var lineA = mappingA.generatedLine;\n\t  var lineB = mappingB.generatedLine;\n\t  var columnA = mappingA.generatedColumn;\n\t  var columnB = mappingB.generatedColumn;\n\t  return lineB > lineA || lineB == lineA && columnB >= columnA ||\n\t         util.compareByGeneratedPositionsInflated(mappingA, mappingB) <= 0;\n\t}\n\t\n\t/**\n\t * A data structure to provide a sorted view of accumulated mappings in a\n\t * performance conscious manner. It trades a neglibable overhead in general\n\t * case for a large speedup in case of mappings being added in order.\n\t */\n\tfunction MappingList() {\n\t  this._array = [];\n\t  this._sorted = true;\n\t  // Serves as infimum\n\t  this._last = {generatedLine: -1, generatedColumn: 0};\n\t}\n\t\n\t/**\n\t * Iterate through internal items. This method takes the same arguments that\n\t * `Array.prototype.forEach` takes.\n\t *\n\t * NOTE: The order of the mappings is NOT guaranteed.\n\t */\n\tMappingList.prototype.unsortedForEach =\n\t  function MappingList_forEach(aCallback, aThisArg) {\n\t    this._array.forEach(aCallback, aThisArg);\n\t  };\n\t\n\t/**\n\t * Add the given source mapping.\n\t *\n\t * @param Object aMapping\n\t */\n\tMappingList.prototype.add = function MappingList_add(aMapping) {\n\t  if (generatedPositionAfter(this._last, aMapping)) {\n\t    this._last = aMapping;\n\t    this._array.push(aMapping);\n\t  } else {\n\t    this._sorted = false;\n\t    this._array.push(aMapping);\n\t  }\n\t};\n\t\n\t/**\n\t * Returns the flat, sorted array of mappings. The mappings are sorted by\n\t * generated position.\n\t *\n\t * WARNING: This method returns internal data without copying, for\n\t * performance. The return value must NOT be mutated, and should be treated as\n\t * an immutable borrow. If you want to take ownership, you must make your own\n\t * copy.\n\t */\n\tMappingList.prototype.toArray = function MappingList_toArray() {\n\t  if (!this._sorted) {\n\t    this._array.sort(util.compareByGeneratedPositionsInflated);\n\t    this._sorted = true;\n\t  }\n\t  return this._array;\n\t};\n\t\n\texports.MappingList = MappingList;\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* -*- Mode: js; js-indent-level: 2; -*- */\n\t/*\n\t * Copyright 2011 Mozilla Foundation and contributors\n\t * Licensed under the New BSD license. See LICENSE or:\n\t * http://opensource.org/licenses/BSD-3-Clause\n\t */\n\t\n\tvar util = __webpack_require__(4);\n\tvar binarySearch = __webpack_require__(8);\n\tvar ArraySet = __webpack_require__(5).ArraySet;\n\tvar base64VLQ = __webpack_require__(2);\n\tvar quickSort = __webpack_require__(9).quickSort;\n\t\n\tfunction SourceMapConsumer(aSourceMap, aSourceMapURL) {\n\t  var sourceMap = aSourceMap;\n\t  if (typeof aSourceMap === 'string') {\n\t    sourceMap = util.parseSourceMapInput(aSourceMap);\n\t  }\n\t\n\t  return sourceMap.sections != null\n\t    ? new IndexedSourceMapConsumer(sourceMap, aSourceMapURL)\n\t    : new BasicSourceMapConsumer(sourceMap, aSourceMapURL);\n\t}\n\t\n\tSourceMapConsumer.fromSourceMap = function(aSourceMap, aSourceMapURL) {\n\t  return BasicSourceMapConsumer.fromSourceMap(aSourceMap, aSourceMapURL);\n\t}\n\t\n\t/**\n\t * The version of the source mapping spec that we are consuming.\n\t */\n\tSourceMapConsumer.prototype._version = 3;\n\t\n\t// `__generatedMappings` and `__originalMappings` are arrays that hold the\n\t// parsed mapping coordinates from the source map's \"mappings\" attribute. They\n\t// are lazily instantiated, accessed via the `_generatedMappings` and\n\t// `_originalMappings` getters respectively, and we only parse the mappings\n\t// and create these arrays once queried for a source location. We jump through\n\t// these hoops because there can be many thousands of mappings, and parsing\n\t// them is expensive, so we only want to do it if we must.\n\t//\n\t// Each object in the arrays is of the form:\n\t//\n\t//     {\n\t//       generatedLine: The line number in the generated code,\n\t//       generatedColumn: The column number in the generated code,\n\t//       source: The path to the original source file that generated this\n\t//               chunk of code,\n\t//       originalLine: The line number in the original source that\n\t//                     corresponds to this chunk of generated code,\n\t//       originalColumn: The column number in the original source that\n\t//                       corresponds to this chunk of generated code,\n\t//       name: The name of the original symbol which generated this chunk of\n\t//             code.\n\t//     }\n\t//\n\t// All properties except for `generatedLine` and `generatedColumn` can be\n\t// `null`.\n\t//\n\t// `_generatedMappings` is ordered by the generated positions.\n\t//\n\t// `_originalMappings` is ordered by the original positions.\n\t\n\tSourceMapConsumer.prototype.__generatedMappings = null;\n\tObject.defineProperty(SourceMapConsumer.prototype, '_generatedMappings', {\n\t  configurable: true,\n\t  enumerable: true,\n\t  get: function () {\n\t    if (!this.__generatedMappings) {\n\t      this._parseMappings(this._mappings, this.sourceRoot);\n\t    }\n\t\n\t    return this.__generatedMappings;\n\t  }\n\t});\n\t\n\tSourceMapConsumer.prototype.__originalMappings = null;\n\tObject.defineProperty(SourceMapConsumer.prototype, '_originalMappings', {\n\t  configurable: true,\n\t  enumerable: true,\n\t  get: function () {\n\t    if (!this.__originalMappings) {\n\t      this._parseMappings(this._mappings, this.sourceRoot);\n\t    }\n\t\n\t    return this.__originalMappings;\n\t  }\n\t});\n\t\n\tSourceMapConsumer.prototype._charIsMappingSeparator =\n\t  function SourceMapConsumer_charIsMappingSeparator(aStr, index) {\n\t    var c = aStr.charAt(index);\n\t    return c === \";\" || c === \",\";\n\t  };\n\t\n\t/**\n\t * Parse the mappings in a string in to a data structure which we can easily\n\t * query (the ordered arrays in the `this.__generatedMappings` and\n\t * `this.__originalMappings` properties).\n\t */\n\tSourceMapConsumer.prototype._parseMappings =\n\t  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n\t    throw new Error(\"Subclasses must implement _parseMappings\");\n\t  };\n\t\n\tSourceMapConsumer.GENERATED_ORDER = 1;\n\tSourceMapConsumer.ORIGINAL_ORDER = 2;\n\t\n\tSourceMapConsumer.GREATEST_LOWER_BOUND = 1;\n\tSourceMapConsumer.LEAST_UPPER_BOUND = 2;\n\t\n\t/**\n\t * Iterate over each mapping between an original source/line/column and a\n\t * generated line/column in this source map.\n\t *\n\t * @param Function aCallback\n\t *        The function that is called with each mapping.\n\t * @param Object aContext\n\t *        Optional. If specified, this object will be the value of `this` every\n\t *        time that `aCallback` is called.\n\t * @param aOrder\n\t *        Either `SourceMapConsumer.GENERATED_ORDER` or\n\t *        `SourceMapConsumer.ORIGINAL_ORDER`. Specifies whether you want to\n\t *        iterate over the mappings sorted by the generated file's line/column\n\t *        order or the original's source/line/column order, respectively. Defaults to\n\t *        `SourceMapConsumer.GENERATED_ORDER`.\n\t */\n\tSourceMapConsumer.prototype.eachMapping =\n\t  function SourceMapConsumer_eachMapping(aCallback, aContext, aOrder) {\n\t    var context = aContext || null;\n\t    var order = aOrder || SourceMapConsumer.GENERATED_ORDER;\n\t\n\t    var mappings;\n\t    switch (order) {\n\t    case SourceMapConsumer.GENERATED_ORDER:\n\t      mappings = this._generatedMappings;\n\t      break;\n\t    case SourceMapConsumer.ORIGINAL_ORDER:\n\t      mappings = this._originalMappings;\n\t      break;\n\t    default:\n\t      throw new Error(\"Unknown order of iteration.\");\n\t    }\n\t\n\t    var sourceRoot = this.sourceRoot;\n\t    mappings.map(function (mapping) {\n\t      var source = mapping.source === null ? null : this._sources.at(mapping.source);\n\t      source = util.computeSourceURL(sourceRoot, source, this._sourceMapURL);\n\t      return {\n\t        source: source,\n\t        generatedLine: mapping.generatedLine,\n\t        generatedColumn: mapping.generatedColumn,\n\t        originalLine: mapping.originalLine,\n\t        originalColumn: mapping.originalColumn,\n\t        name: mapping.name === null ? null : this._names.at(mapping.name)\n\t      };\n\t    }, this).forEach(aCallback, context);\n\t  };\n\t\n\t/**\n\t * Returns all generated line and column information for the original source,\n\t * line, and column provided. If no column is provided, returns all mappings\n\t * corresponding to a either the line we are searching for or the next\n\t * closest line that has any mappings. Otherwise, returns all mappings\n\t * corresponding to the given line and either the column we are searching for\n\t * or the next closest column that has any offsets.\n\t *\n\t * The only argument is an object with the following properties:\n\t *\n\t *   - source: The filename of the original source.\n\t *   - line: The line number in the original source.  The line number is 1-based.\n\t *   - column: Optional. the column number in the original source.\n\t *    The column number is 0-based.\n\t *\n\t * and an array of objects is returned, each with the following properties:\n\t *\n\t *   - line: The line number in the generated source, or null.  The\n\t *    line number is 1-based.\n\t *   - column: The column number in the generated source, or null.\n\t *    The column number is 0-based.\n\t */\n\tSourceMapConsumer.prototype.allGeneratedPositionsFor =\n\t  function SourceMapConsumer_allGeneratedPositionsFor(aArgs) {\n\t    var line = util.getArg(aArgs, 'line');\n\t\n\t    // When there is no exact match, BasicSourceMapConsumer.prototype._findMapping\n\t    // returns the index of the closest mapping less than the needle. By\n\t    // setting needle.originalColumn to 0, we thus find the last mapping for\n\t    // the given line, provided such a mapping exists.\n\t    var needle = {\n\t      source: util.getArg(aArgs, 'source'),\n\t      originalLine: line,\n\t      originalColumn: util.getArg(aArgs, 'column', 0)\n\t    };\n\t\n\t    needle.source = this._findSourceIndex(needle.source);\n\t    if (needle.source < 0) {\n\t      return [];\n\t    }\n\t\n\t    var mappings = [];\n\t\n\t    var index = this._findMapping(needle,\n\t                                  this._originalMappings,\n\t                                  \"originalLine\",\n\t                                  \"originalColumn\",\n\t                                  util.compareByOriginalPositions,\n\t                                  binarySearch.LEAST_UPPER_BOUND);\n\t    if (index >= 0) {\n\t      var mapping = this._originalMappings[index];\n\t\n\t      if (aArgs.column === undefined) {\n\t        var originalLine = mapping.originalLine;\n\t\n\t        // Iterate until either we run out of mappings, or we run into\n\t        // a mapping for a different line than the one we found. Since\n\t        // mappings are sorted, this is guaranteed to find all mappings for\n\t        // the line we found.\n\t        while (mapping && mapping.originalLine === originalLine) {\n\t          mappings.push({\n\t            line: util.getArg(mapping, 'generatedLine', null),\n\t            column: util.getArg(mapping, 'generatedColumn', null),\n\t            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n\t          });\n\t\n\t          mapping = this._originalMappings[++index];\n\t        }\n\t      } else {\n\t        var originalColumn = mapping.originalColumn;\n\t\n\t        // Iterate until either we run out of mappings, or we run into\n\t        // a mapping for a different line than the one we were searching for.\n\t        // Since mappings are sorted, this is guaranteed to find all mappings for\n\t        // the line we are searching for.\n\t        while (mapping &&\n\t               mapping.originalLine === line &&\n\t               mapping.originalColumn == originalColumn) {\n\t          mappings.push({\n\t            line: util.getArg(mapping, 'generatedLine', null),\n\t            column: util.getArg(mapping, 'generatedColumn', null),\n\t            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n\t          });\n\t\n\t          mapping = this._originalMappings[++index];\n\t        }\n\t      }\n\t    }\n\t\n\t    return mappings;\n\t  };\n\t\n\texports.SourceMapConsumer = SourceMapConsumer;\n\t\n\t/**\n\t * A BasicSourceMapConsumer instance represents a parsed source map which we can\n\t * query for information about the original file positions by giving it a file\n\t * position in the generated source.\n\t *\n\t * The first parameter is the raw source map (either as a JSON string, or\n\t * already parsed to an object). According to the spec, source maps have the\n\t * following attributes:\n\t *\n\t *   - version: Which version of the source map spec this map is following.\n\t *   - sources: An array of URLs to the original source files.\n\t *   - names: An array of identifiers which can be referrenced by individual mappings.\n\t *   - sourceRoot: Optional. The URL root from which all sources are relative.\n\t *   - sourcesContent: Optional. An array of contents of the original source files.\n\t *   - mappings: A string of base64 VLQs which contain the actual mappings.\n\t *   - file: Optional. The generated file this source map is associated with.\n\t *\n\t * Here is an example source map, taken from the source map spec[0]:\n\t *\n\t *     {\n\t *       version : 3,\n\t *       file: \"out.js\",\n\t *       sourceRoot : \"\",\n\t *       sources: [\"foo.js\", \"bar.js\"],\n\t *       names: [\"src\", \"maps\", \"are\", \"fun\"],\n\t *       mappings: \"AA,AB;;ABCDE;\"\n\t *     }\n\t *\n\t * The second parameter, if given, is a string whose value is the URL\n\t * at which the source map was found.  This URL is used to compute the\n\t * sources array.\n\t *\n\t * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit?pli=1#\n\t */\n\tfunction BasicSourceMapConsumer(aSourceMap, aSourceMapURL) {\n\t  var sourceMap = aSourceMap;\n\t  if (typeof aSourceMap === 'string') {\n\t    sourceMap = util.parseSourceMapInput(aSourceMap);\n\t  }\n\t\n\t  var version = util.getArg(sourceMap, 'version');\n\t  var sources = util.getArg(sourceMap, 'sources');\n\t  // Sass 3.3 leaves out the 'names' array, so we deviate from the spec (which\n\t  // requires the array) to play nice here.\n\t  var names = util.getArg(sourceMap, 'names', []);\n\t  var sourceRoot = util.getArg(sourceMap, 'sourceRoot', null);\n\t  var sourcesContent = util.getArg(sourceMap, 'sourcesContent', null);\n\t  var mappings = util.getArg(sourceMap, 'mappings');\n\t  var file = util.getArg(sourceMap, 'file', null);\n\t\n\t  // Once again, Sass deviates from the spec and supplies the version as a\n\t  // string rather than a number, so we use loose equality checking here.\n\t  if (version != this._version) {\n\t    throw new Error('Unsupported version: ' + version);\n\t  }\n\t\n\t  if (sourceRoot) {\n\t    sourceRoot = util.normalize(sourceRoot);\n\t  }\n\t\n\t  sources = sources\n\t    .map(String)\n\t    // Some source maps produce relative source paths like \"./foo.js\" instead of\n\t    // \"foo.js\".  Normalize these first so that future comparisons will succeed.\n\t    // See bugzil.la/1090768.\n\t    .map(util.normalize)\n\t    // Always ensure that absolute sources are internally stored relative to\n\t    // the source root, if the source root is absolute. Not doing this would\n\t    // be particularly problematic when the source root is a prefix of the\n\t    // source (valid, but why??). See github issue #199 and bugzil.la/1188982.\n\t    .map(function (source) {\n\t      return sourceRoot && util.isAbsolute(sourceRoot) && util.isAbsolute(source)\n\t        ? util.relative(sourceRoot, source)\n\t        : source;\n\t    });\n\t\n\t  // Pass `true` below to allow duplicate names and sources. While source maps\n\t  // are intended to be compressed and deduplicated, the TypeScript compiler\n\t  // sometimes generates source maps with duplicates in them. See Github issue\n\t  // #72 and bugzil.la/889492.\n\t  this._names = ArraySet.fromArray(names.map(String), true);\n\t  this._sources = ArraySet.fromArray(sources, true);\n\t\n\t  this._absoluteSources = this._sources.toArray().map(function (s) {\n\t    return util.computeSourceURL(sourceRoot, s, aSourceMapURL);\n\t  });\n\t\n\t  this.sourceRoot = sourceRoot;\n\t  this.sourcesContent = sourcesContent;\n\t  this._mappings = mappings;\n\t  this._sourceMapURL = aSourceMapURL;\n\t  this.file = file;\n\t}\n\t\n\tBasicSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\n\tBasicSourceMapConsumer.prototype.consumer = SourceMapConsumer;\n\t\n\t/**\n\t * Utility function to find the index of a source.  Returns -1 if not\n\t * found.\n\t */\n\tBasicSourceMapConsumer.prototype._findSourceIndex = function(aSource) {\n\t  var relativeSource = aSource;\n\t  if (this.sourceRoot != null) {\n\t    relativeSource = util.relative(this.sourceRoot, relativeSource);\n\t  }\n\t\n\t  if (this._sources.has(relativeSource)) {\n\t    return this._sources.indexOf(relativeSource);\n\t  }\n\t\n\t  // Maybe aSource is an absolute URL as returned by |sources|.  In\n\t  // this case we can't simply undo the transform.\n\t  var i;\n\t  for (i = 0; i < this._absoluteSources.length; ++i) {\n\t    if (this._absoluteSources[i] == aSource) {\n\t      return i;\n\t    }\n\t  }\n\t\n\t  return -1;\n\t};\n\t\n\t/**\n\t * Create a BasicSourceMapConsumer from a SourceMapGenerator.\n\t *\n\t * @param SourceMapGenerator aSourceMap\n\t *        The source map that will be consumed.\n\t * @param String aSourceMapURL\n\t *        The URL at which the source map can be found (optional)\n\t * @returns BasicSourceMapConsumer\n\t */\n\tBasicSourceMapConsumer.fromSourceMap =\n\t  function SourceMapConsumer_fromSourceMap(aSourceMap, aSourceMapURL) {\n\t    var smc = Object.create(BasicSourceMapConsumer.prototype);\n\t\n\t    var names = smc._names = ArraySet.fromArray(aSourceMap._names.toArray(), true);\n\t    var sources = smc._sources = ArraySet.fromArray(aSourceMap._sources.toArray(), true);\n\t    smc.sourceRoot = aSourceMap._sourceRoot;\n\t    smc.sourcesContent = aSourceMap._generateSourcesContent(smc._sources.toArray(),\n\t                                                            smc.sourceRoot);\n\t    smc.file = aSourceMap._file;\n\t    smc._sourceMapURL = aSourceMapURL;\n\t    smc._absoluteSources = smc._sources.toArray().map(function (s) {\n\t      return util.computeSourceURL(smc.sourceRoot, s, aSourceMapURL);\n\t    });\n\t\n\t    // Because we are modifying the entries (by converting string sources and\n\t    // names to indices into the sources and names ArraySets), we have to make\n\t    // a copy of the entry or else bad things happen. Shared mutable state\n\t    // strikes again! See github issue #191.\n\t\n\t    var generatedMappings = aSourceMap._mappings.toArray().slice();\n\t    var destGeneratedMappings = smc.__generatedMappings = [];\n\t    var destOriginalMappings = smc.__originalMappings = [];\n\t\n\t    for (var i = 0, length = generatedMappings.length; i < length; i++) {\n\t      var srcMapping = generatedMappings[i];\n\t      var destMapping = new Mapping;\n\t      destMapping.generatedLine = srcMapping.generatedLine;\n\t      destMapping.generatedColumn = srcMapping.generatedColumn;\n\t\n\t      if (srcMapping.source) {\n\t        destMapping.source = sources.indexOf(srcMapping.source);\n\t        destMapping.originalLine = srcMapping.originalLine;\n\t        destMapping.originalColumn = srcMapping.originalColumn;\n\t\n\t        if (srcMapping.name) {\n\t          destMapping.name = names.indexOf(srcMapping.name);\n\t        }\n\t\n\t        destOriginalMappings.push(destMapping);\n\t      }\n\t\n\t      destGeneratedMappings.push(destMapping);\n\t    }\n\t\n\t    quickSort(smc.__originalMappings, util.compareByOriginalPositions);\n\t\n\t    return smc;\n\t  };\n\t\n\t/**\n\t * The version of the source mapping spec that we are consuming.\n\t */\n\tBasicSourceMapConsumer.prototype._version = 3;\n\t\n\t/**\n\t * The list of original sources.\n\t */\n\tObject.defineProperty(BasicSourceMapConsumer.prototype, 'sources', {\n\t  get: function () {\n\t    return this._absoluteSources.slice();\n\t  }\n\t});\n\t\n\t/**\n\t * Provide the JIT with a nice shape / hidden class.\n\t */\n\tfunction Mapping() {\n\t  this.generatedLine = 0;\n\t  this.generatedColumn = 0;\n\t  this.source = null;\n\t  this.originalLine = null;\n\t  this.originalColumn = null;\n\t  this.name = null;\n\t}\n\t\n\t/**\n\t * Parse the mappings in a string in to a data structure which we can easily\n\t * query (the ordered arrays in the `this.__generatedMappings` and\n\t * `this.__originalMappings` properties).\n\t */\n\tBasicSourceMapConsumer.prototype._parseMappings =\n\t  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n\t    var generatedLine = 1;\n\t    var previousGeneratedColumn = 0;\n\t    var previousOriginalLine = 0;\n\t    var previousOriginalColumn = 0;\n\t    var previousSource = 0;\n\t    var previousName = 0;\n\t    var length = aStr.length;\n\t    var index = 0;\n\t    var cachedSegments = {};\n\t    var temp = {};\n\t    var originalMappings = [];\n\t    var generatedMappings = [];\n\t    var mapping, str, segment, end, value;\n\t\n\t    while (index < length) {\n\t      if (aStr.charAt(index) === ';') {\n\t        generatedLine++;\n\t        index++;\n\t        previousGeneratedColumn = 0;\n\t      }\n\t      else if (aStr.charAt(index) === ',') {\n\t        index++;\n\t      }\n\t      else {\n\t        mapping = new Mapping();\n\t        mapping.generatedLine = generatedLine;\n\t\n\t        // Because each offset is encoded relative to the previous one,\n\t        // many segments often have the same encoding. We can exploit this\n\t        // fact by caching the parsed variable length fields of each segment,\n\t        // allowing us to avoid a second parse if we encounter the same\n\t        // segment again.\n\t        for (end = index; end < length; end++) {\n\t          if (this._charIsMappingSeparator(aStr, end)) {\n\t            break;\n\t          }\n\t        }\n\t        str = aStr.slice(index, end);\n\t\n\t        segment = cachedSegments[str];\n\t        if (segment) {\n\t          index += str.length;\n\t        } else {\n\t          segment = [];\n\t          while (index < end) {\n\t            base64VLQ.decode(aStr, index, temp);\n\t            value = temp.value;\n\t            index = temp.rest;\n\t            segment.push(value);\n\t          }\n\t\n\t          if (segment.length === 2) {\n\t            throw new Error('Found a source, but no line and column');\n\t          }\n\t\n\t          if (segment.length === 3) {\n\t            throw new Error('Found a source and line, but no column');\n\t          }\n\t\n\t          cachedSegments[str] = segment;\n\t        }\n\t\n\t        // Generated column.\n\t        mapping.generatedColumn = previousGeneratedColumn + segment[0];\n\t        previousGeneratedColumn = mapping.generatedColumn;\n\t\n\t        if (segment.length > 1) {\n\t          // Original source.\n\t          mapping.source = previousSource + segment[1];\n\t          previousSource += segment[1];\n\t\n\t          // Original line.\n\t          mapping.originalLine = previousOriginalLine + segment[2];\n\t          previousOriginalLine = mapping.originalLine;\n\t          // Lines are stored 0-based\n\t          mapping.originalLine += 1;\n\t\n\t          // Original column.\n\t          mapping.originalColumn = previousOriginalColumn + segment[3];\n\t          previousOriginalColumn = mapping.originalColumn;\n\t\n\t          if (segment.length > 4) {\n\t            // Original name.\n\t            mapping.name = previousName + segment[4];\n\t            previousName += segment[4];\n\t          }\n\t        }\n\t\n\t        generatedMappings.push(mapping);\n\t        if (typeof mapping.originalLine === 'number') {\n\t          originalMappings.push(mapping);\n\t        }\n\t      }\n\t    }\n\t\n\t    quickSort(generatedMappings, util.compareByGeneratedPositionsDeflated);\n\t    this.__generatedMappings = generatedMappings;\n\t\n\t    quickSort(originalMappings, util.compareByOriginalPositions);\n\t    this.__originalMappings = originalMappings;\n\t  };\n\t\n\t/**\n\t * Find the mapping that best matches the hypothetical \"needle\" mapping that\n\t * we are searching for in the given \"haystack\" of mappings.\n\t */\n\tBasicSourceMapConsumer.prototype._findMapping =\n\t  function SourceMapConsumer_findMapping(aNeedle, aMappings, aLineName,\n\t                                         aColumnName, aComparator, aBias) {\n\t    // To return the position we are searching for, we must first find the\n\t    // mapping for the given position and then return the opposite position it\n\t    // points to. Because the mappings are sorted, we can use binary search to\n\t    // find the best mapping.\n\t\n\t    if (aNeedle[aLineName] <= 0) {\n\t      throw new TypeError('Line must be greater than or equal to 1, got '\n\t                          + aNeedle[aLineName]);\n\t    }\n\t    if (aNeedle[aColumnName] < 0) {\n\t      throw new TypeError('Column must be greater than or equal to 0, got '\n\t                          + aNeedle[aColumnName]);\n\t    }\n\t\n\t    return binarySearch.search(aNeedle, aMappings, aComparator, aBias);\n\t  };\n\t\n\t/**\n\t * Compute the last column for each generated mapping. The last column is\n\t * inclusive.\n\t */\n\tBasicSourceMapConsumer.prototype.computeColumnSpans =\n\t  function SourceMapConsumer_computeColumnSpans() {\n\t    for (var index = 0; index < this._generatedMappings.length; ++index) {\n\t      var mapping = this._generatedMappings[index];\n\t\n\t      // Mappings do not contain a field for the last generated columnt. We\n\t      // can come up with an optimistic estimate, however, by assuming that\n\t      // mappings are contiguous (i.e. given two consecutive mappings, the\n\t      // first mapping ends where the second one starts).\n\t      if (index + 1 < this._generatedMappings.length) {\n\t        var nextMapping = this._generatedMappings[index + 1];\n\t\n\t        if (mapping.generatedLine === nextMapping.generatedLine) {\n\t          mapping.lastGeneratedColumn = nextMapping.generatedColumn - 1;\n\t          continue;\n\t        }\n\t      }\n\t\n\t      // The last mapping for each line spans the entire line.\n\t      mapping.lastGeneratedColumn = Infinity;\n\t    }\n\t  };\n\t\n\t/**\n\t * Returns the original source, line, and column information for the generated\n\t * source's line and column positions provided. The only argument is an object\n\t * with the following properties:\n\t *\n\t *   - line: The line number in the generated source.  The line number\n\t *     is 1-based.\n\t *   - column: The column number in the generated source.  The column\n\t *     number is 0-based.\n\t *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n\t *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n\t *     closest element that is smaller than or greater than the one we are\n\t *     searching for, respectively, if the exact element cannot be found.\n\t *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n\t *\n\t * and an object is returned with the following properties:\n\t *\n\t *   - source: The original source file, or null.\n\t *   - line: The line number in the original source, or null.  The\n\t *     line number is 1-based.\n\t *   - column: The column number in the original source, or null.  The\n\t *     column number is 0-based.\n\t *   - name: The original identifier, or null.\n\t */\n\tBasicSourceMapConsumer.prototype.originalPositionFor =\n\t  function SourceMapConsumer_originalPositionFor(aArgs) {\n\t    var needle = {\n\t      generatedLine: util.getArg(aArgs, 'line'),\n\t      generatedColumn: util.getArg(aArgs, 'column')\n\t    };\n\t\n\t    var index = this._findMapping(\n\t      needle,\n\t      this._generatedMappings,\n\t      \"generatedLine\",\n\t      \"generatedColumn\",\n\t      util.compareByGeneratedPositionsDeflated,\n\t      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n\t    );\n\t\n\t    if (index >= 0) {\n\t      var mapping = this._generatedMappings[index];\n\t\n\t      if (mapping.generatedLine === needle.generatedLine) {\n\t        var source = util.getArg(mapping, 'source', null);\n\t        if (source !== null) {\n\t          source = this._sources.at(source);\n\t          source = util.computeSourceURL(this.sourceRoot, source, this._sourceMapURL);\n\t        }\n\t        var name = util.getArg(mapping, 'name', null);\n\t        if (name !== null) {\n\t          name = this._names.at(name);\n\t        }\n\t        return {\n\t          source: source,\n\t          line: util.getArg(mapping, 'originalLine', null),\n\t          column: util.getArg(mapping, 'originalColumn', null),\n\t          name: name\n\t        };\n\t      }\n\t    }\n\t\n\t    return {\n\t      source: null,\n\t      line: null,\n\t      column: null,\n\t      name: null\n\t    };\n\t  };\n\t\n\t/**\n\t * Return true if we have the source content for every source in the source\n\t * map, false otherwise.\n\t */\n\tBasicSourceMapConsumer.prototype.hasContentsOfAllSources =\n\t  function BasicSourceMapConsumer_hasContentsOfAllSources() {\n\t    if (!this.sourcesContent) {\n\t      return false;\n\t    }\n\t    return this.sourcesContent.length >= this._sources.size() &&\n\t      !this.sourcesContent.some(function (sc) { return sc == null; });\n\t  };\n\t\n\t/**\n\t * Returns the original source content. The only argument is the url of the\n\t * original source file. Returns null if no original source content is\n\t * available.\n\t */\n\tBasicSourceMapConsumer.prototype.sourceContentFor =\n\t  function SourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n\t    if (!this.sourcesContent) {\n\t      return null;\n\t    }\n\t\n\t    var index = this._findSourceIndex(aSource);\n\t    if (index >= 0) {\n\t      return this.sourcesContent[index];\n\t    }\n\t\n\t    var relativeSource = aSource;\n\t    if (this.sourceRoot != null) {\n\t      relativeSource = util.relative(this.sourceRoot, relativeSource);\n\t    }\n\t\n\t    var url;\n\t    if (this.sourceRoot != null\n\t        && (url = util.urlParse(this.sourceRoot))) {\n\t      // XXX: file:// URIs and absolute paths lead to unexpected behavior for\n\t      // many users. We can help them out when they expect file:// URIs to\n\t      // behave like it would if they were running a local HTTP server. See\n\t      // https://bugzilla.mozilla.org/show_bug.cgi?id=885597.\n\t      var fileUriAbsPath = relativeSource.replace(/^file:\\/\\//, \"\");\n\t      if (url.scheme == \"file\"\n\t          && this._sources.has(fileUriAbsPath)) {\n\t        return this.sourcesContent[this._sources.indexOf(fileUriAbsPath)]\n\t      }\n\t\n\t      if ((!url.path || url.path == \"/\")\n\t          && this._sources.has(\"/\" + relativeSource)) {\n\t        return this.sourcesContent[this._sources.indexOf(\"/\" + relativeSource)];\n\t      }\n\t    }\n\t\n\t    // This function is used recursively from\n\t    // IndexedSourceMapConsumer.prototype.sourceContentFor. In that case, we\n\t    // don't want to throw if we can't find the source - we just want to\n\t    // return null, so we provide a flag to exit gracefully.\n\t    if (nullOnMissing) {\n\t      return null;\n\t    }\n\t    else {\n\t      throw new Error('\"' + relativeSource + '\" is not in the SourceMap.');\n\t    }\n\t  };\n\t\n\t/**\n\t * Returns the generated line and column information for the original source,\n\t * line, and column positions provided. The only argument is an object with\n\t * the following properties:\n\t *\n\t *   - source: The filename of the original source.\n\t *   - line: The line number in the original source.  The line number\n\t *     is 1-based.\n\t *   - column: The column number in the original source.  The column\n\t *     number is 0-based.\n\t *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n\t *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n\t *     closest element that is smaller than or greater than the one we are\n\t *     searching for, respectively, if the exact element cannot be found.\n\t *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n\t *\n\t * and an object is returned with the following properties:\n\t *\n\t *   - line: The line number in the generated source, or null.  The\n\t *     line number is 1-based.\n\t *   - column: The column number in the generated source, or null.\n\t *     The column number is 0-based.\n\t */\n\tBasicSourceMapConsumer.prototype.generatedPositionFor =\n\t  function SourceMapConsumer_generatedPositionFor(aArgs) {\n\t    var source = util.getArg(aArgs, 'source');\n\t    source = this._findSourceIndex(source);\n\t    if (source < 0) {\n\t      return {\n\t        line: null,\n\t        column: null,\n\t        lastColumn: null\n\t      };\n\t    }\n\t\n\t    var needle = {\n\t      source: source,\n\t      originalLine: util.getArg(aArgs, 'line'),\n\t      originalColumn: util.getArg(aArgs, 'column')\n\t    };\n\t\n\t    var index = this._findMapping(\n\t      needle,\n\t      this._originalMappings,\n\t      \"originalLine\",\n\t      \"originalColumn\",\n\t      util.compareByOriginalPositions,\n\t      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n\t    );\n\t\n\t    if (index >= 0) {\n\t      var mapping = this._originalMappings[index];\n\t\n\t      if (mapping.source === needle.source) {\n\t        return {\n\t          line: util.getArg(mapping, 'generatedLine', null),\n\t          column: util.getArg(mapping, 'generatedColumn', null),\n\t          lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n\t        };\n\t      }\n\t    }\n\t\n\t    return {\n\t      line: null,\n\t      column: null,\n\t      lastColumn: null\n\t    };\n\t  };\n\t\n\texports.BasicSourceMapConsumer = BasicSourceMapConsumer;\n\t\n\t/**\n\t * An IndexedSourceMapConsumer instance represents a parsed source map which\n\t * we can query for information. It differs from BasicSourceMapConsumer in\n\t * that it takes \"indexed\" source maps (i.e. ones with a \"sections\" field) as\n\t * input.\n\t *\n\t * The first parameter is a raw source map (either as a JSON string, or already\n\t * parsed to an object). According to the spec for indexed source maps, they\n\t * have the following attributes:\n\t *\n\t *   - version: Which version of the source map spec this map is following.\n\t *   - file: Optional. The generated file this source map is associated with.\n\t *   - sections: A list of section definitions.\n\t *\n\t * Each value under the \"sections\" field has two fields:\n\t *   - offset: The offset into the original specified at which this section\n\t *       begins to apply, defined as an object with a \"line\" and \"column\"\n\t *       field.\n\t *   - map: A source map definition. This source map could also be indexed,\n\t *       but doesn't have to be.\n\t *\n\t * Instead of the \"map\" field, it's also possible to have a \"url\" field\n\t * specifying a URL to retrieve a source map from, but that's currently\n\t * unsupported.\n\t *\n\t * Here's an example source map, taken from the source map spec[0], but\n\t * modified to omit a section which uses the \"url\" field.\n\t *\n\t *  {\n\t *    version : 3,\n\t *    file: \"app.js\",\n\t *    sections: [{\n\t *      offset: {line:100, column:10},\n\t *      map: {\n\t *        version : 3,\n\t *        file: \"section.js\",\n\t *        sources: [\"foo.js\", \"bar.js\"],\n\t *        names: [\"src\", \"maps\", \"are\", \"fun\"],\n\t *        mappings: \"AAAA,E;;ABCDE;\"\n\t *      }\n\t *    }],\n\t *  }\n\t *\n\t * The second parameter, if given, is a string whose value is the URL\n\t * at which the source map was found.  This URL is used to compute the\n\t * sources array.\n\t *\n\t * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit#heading=h.535es3xeprgt\n\t */\n\tfunction IndexedSourceMapConsumer(aSourceMap, aSourceMapURL) {\n\t  var sourceMap = aSourceMap;\n\t  if (typeof aSourceMap === 'string') {\n\t    sourceMap = util.parseSourceMapInput(aSourceMap);\n\t  }\n\t\n\t  var version = util.getArg(sourceMap, 'version');\n\t  var sections = util.getArg(sourceMap, 'sections');\n\t\n\t  if (version != this._version) {\n\t    throw new Error('Unsupported version: ' + version);\n\t  }\n\t\n\t  this._sources = new ArraySet();\n\t  this._names = new ArraySet();\n\t\n\t  var lastOffset = {\n\t    line: -1,\n\t    column: 0\n\t  };\n\t  this._sections = sections.map(function (s) {\n\t    if (s.url) {\n\t      // The url field will require support for asynchronicity.\n\t      // See https://github.com/mozilla/source-map/issues/16\n\t      throw new Error('Support for url field in sections not implemented.');\n\t    }\n\t    var offset = util.getArg(s, 'offset');\n\t    var offsetLine = util.getArg(offset, 'line');\n\t    var offsetColumn = util.getArg(offset, 'column');\n\t\n\t    if (offsetLine < lastOffset.line ||\n\t        (offsetLine === lastOffset.line && offsetColumn < lastOffset.column)) {\n\t      throw new Error('Section offsets must be ordered and non-overlapping.');\n\t    }\n\t    lastOffset = offset;\n\t\n\t    return {\n\t      generatedOffset: {\n\t        // The offset fields are 0-based, but we use 1-based indices when\n\t        // encoding/decoding from VLQ.\n\t        generatedLine: offsetLine + 1,\n\t        generatedColumn: offsetColumn + 1\n\t      },\n\t      consumer: new SourceMapConsumer(util.getArg(s, 'map'), aSourceMapURL)\n\t    }\n\t  });\n\t}\n\t\n\tIndexedSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\n\tIndexedSourceMapConsumer.prototype.constructor = SourceMapConsumer;\n\t\n\t/**\n\t * The version of the source mapping spec that we are consuming.\n\t */\n\tIndexedSourceMapConsumer.prototype._version = 3;\n\t\n\t/**\n\t * The list of original sources.\n\t */\n\tObject.defineProperty(IndexedSourceMapConsumer.prototype, 'sources', {\n\t  get: function () {\n\t    var sources = [];\n\t    for (var i = 0; i < this._sections.length; i++) {\n\t      for (var j = 0; j < this._sections[i].consumer.sources.length; j++) {\n\t        sources.push(this._sections[i].consumer.sources[j]);\n\t      }\n\t    }\n\t    return sources;\n\t  }\n\t});\n\t\n\t/**\n\t * Returns the original source, line, and column information for the generated\n\t * source's line and column positions provided. The only argument is an object\n\t * with the following properties:\n\t *\n\t *   - line: The line number in the generated source.  The line number\n\t *     is 1-based.\n\t *   - column: The column number in the generated source.  The column\n\t *     number is 0-based.\n\t *\n\t * and an object is returned with the following properties:\n\t *\n\t *   - source: The original source file, or null.\n\t *   - line: The line number in the original source, or null.  The\n\t *     line number is 1-based.\n\t *   - column: The column number in the original source, or null.  The\n\t *     column number is 0-based.\n\t *   - name: The original identifier, or null.\n\t */\n\tIndexedSourceMapConsumer.prototype.originalPositionFor =\n\t  function IndexedSourceMapConsumer_originalPositionFor(aArgs) {\n\t    var needle = {\n\t      generatedLine: util.getArg(aArgs, 'line'),\n\t      generatedColumn: util.getArg(aArgs, 'column')\n\t    };\n\t\n\t    // Find the section containing the generated position we're trying to map\n\t    // to an original position.\n\t    var sectionIndex = binarySearch.search(needle, this._sections,\n\t      function(needle, section) {\n\t        var cmp = needle.generatedLine - section.generatedOffset.generatedLine;\n\t        if (cmp) {\n\t          return cmp;\n\t        }\n\t\n\t        return (needle.generatedColumn -\n\t                section.generatedOffset.generatedColumn);\n\t      });\n\t    var section = this._sections[sectionIndex];\n\t\n\t    if (!section) {\n\t      return {\n\t        source: null,\n\t        line: null,\n\t        column: null,\n\t        name: null\n\t      };\n\t    }\n\t\n\t    return section.consumer.originalPositionFor({\n\t      line: needle.generatedLine -\n\t        (section.generatedOffset.generatedLine - 1),\n\t      column: needle.generatedColumn -\n\t        (section.generatedOffset.generatedLine === needle.generatedLine\n\t         ? section.generatedOffset.generatedColumn - 1\n\t         : 0),\n\t      bias: aArgs.bias\n\t    });\n\t  };\n\t\n\t/**\n\t * Return true if we have the source content for every source in the source\n\t * map, false otherwise.\n\t */\n\tIndexedSourceMapConsumer.prototype.hasContentsOfAllSources =\n\t  function IndexedSourceMapConsumer_hasContentsOfAllSources() {\n\t    return this._sections.every(function (s) {\n\t      return s.consumer.hasContentsOfAllSources();\n\t    });\n\t  };\n\t\n\t/**\n\t * Returns the original source content. The only argument is the url of the\n\t * original source file. Returns null if no original source content is\n\t * available.\n\t */\n\tIndexedSourceMapConsumer.prototype.sourceContentFor =\n\t  function IndexedSourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n\t    for (var i = 0; i < this._sections.length; i++) {\n\t      var section = this._sections[i];\n\t\n\t      var content = section.consumer.sourceContentFor(aSource, true);\n\t      if (content) {\n\t        return content;\n\t      }\n\t    }\n\t    if (nullOnMissing) {\n\t      return null;\n\t    }\n\t    else {\n\t      throw new Error('\"' + aSource + '\" is not in the SourceMap.');\n\t    }\n\t  };\n\t\n\t/**\n\t * Returns the generated line and column information for the original source,\n\t * line, and column positions provided. The only argument is an object with\n\t * the following properties:\n\t *\n\t *   - source: The filename of the original source.\n\t *   - line: The line number in the original source.  The line number\n\t *     is 1-based.\n\t *   - column: The column number in the original source.  The column\n\t *     number is 0-based.\n\t *\n\t * and an object is returned with the following properties:\n\t *\n\t *   - line: The line number in the generated source, or null.  The\n\t *     line number is 1-based. \n\t *   - column: The column number in the generated source, or null.\n\t *     The column number is 0-based.\n\t */\n\tIndexedSourceMapConsumer.prototype.generatedPositionFor =\n\t  function IndexedSourceMapConsumer_generatedPositionFor(aArgs) {\n\t    for (var i = 0; i < this._sections.length; i++) {\n\t      var section = this._sections[i];\n\t\n\t      // Only consider this section if the requested source is in the list of\n\t      // sources of the consumer.\n\t      if (section.consumer._findSourceIndex(util.getArg(aArgs, 'source')) === -1) {\n\t        continue;\n\t      }\n\t      var generatedPosition = section.consumer.generatedPositionFor(aArgs);\n\t      if (generatedPosition) {\n\t        var ret = {\n\t          line: generatedPosition.line +\n\t            (section.generatedOffset.generatedLine - 1),\n\t          column: generatedPosition.column +\n\t            (section.generatedOffset.generatedLine === generatedPosition.line\n\t             ? section.generatedOffset.generatedColumn - 1\n\t             : 0)\n\t        };\n\t        return ret;\n\t      }\n\t    }\n\t\n\t    return {\n\t      line: null,\n\t      column: null\n\t    };\n\t  };\n\t\n\t/**\n\t * Parse the mappings in a string in to a data structure which we can easily\n\t * query (the ordered arrays in the `this.__generatedMappings` and\n\t * `this.__originalMappings` properties).\n\t */\n\tIndexedSourceMapConsumer.prototype._parseMappings =\n\t  function IndexedSourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n\t    this.__generatedMappings = [];\n\t    this.__originalMappings = [];\n\t    for (var i = 0; i < this._sections.length; i++) {\n\t      var section = this._sections[i];\n\t      var sectionMappings = section.consumer._generatedMappings;\n\t      for (var j = 0; j < sectionMappings.length; j++) {\n\t        var mapping = sectionMappings[j];\n\t\n\t        var source = section.consumer._sources.at(mapping.source);\n\t        source = util.computeSourceURL(section.consumer.sourceRoot, source, this._sourceMapURL);\n\t        this._sources.add(source);\n\t        source = this._sources.indexOf(source);\n\t\n\t        var name = null;\n\t        if (mapping.name) {\n\t          name = section.consumer._names.at(mapping.name);\n\t          this._names.add(name);\n\t          name = this._names.indexOf(name);\n\t        }\n\t\n\t        // The mappings coming from the consumer for the section have\n\t        // generated positions relative to the start of the section, so we\n\t        // need to offset them to be relative to the start of the concatenated\n\t        // generated file.\n\t        var adjustedMapping = {\n\t          source: source,\n\t          generatedLine: mapping.generatedLine +\n\t            (section.generatedOffset.generatedLine - 1),\n\t          generatedColumn: mapping.generatedColumn +\n\t            (section.generatedOffset.generatedLine === mapping.generatedLine\n\t            ? section.generatedOffset.generatedColumn - 1\n\t            : 0),\n\t          originalLine: mapping.originalLine,\n\t          originalColumn: mapping.originalColumn,\n\t          name: name\n\t        };\n\t\n\t        this.__generatedMappings.push(adjustedMapping);\n\t        if (typeof adjustedMapping.originalLine === 'number') {\n\t          this.__originalMappings.push(adjustedMapping);\n\t        }\n\t      }\n\t    }\n\t\n\t    quickSort(this.__generatedMappings, util.compareByGeneratedPositionsDeflated);\n\t    quickSort(this.__originalMappings, util.compareByOriginalPositions);\n\t  };\n\t\n\texports.IndexedSourceMapConsumer = IndexedSourceMapConsumer;\n\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports) {\n\n\t/* -*- Mode: js; js-indent-level: 2; -*- */\n\t/*\n\t * Copyright 2011 Mozilla Foundation and contributors\n\t * Licensed under the New BSD license. See LICENSE or:\n\t * http://opensource.org/licenses/BSD-3-Clause\n\t */\n\t\n\texports.GREATEST_LOWER_BOUND = 1;\n\texports.LEAST_UPPER_BOUND = 2;\n\t\n\t/**\n\t * Recursive implementation of binary search.\n\t *\n\t * @param aLow Indices here and lower do not contain the needle.\n\t * @param aHigh Indices here and higher do not contain the needle.\n\t * @param aNeedle The element being searched for.\n\t * @param aHaystack The non-empty array being searched.\n\t * @param aCompare Function which takes two elements and returns -1, 0, or 1.\n\t * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n\t *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n\t *     closest element that is smaller than or greater than the one we are\n\t *     searching for, respectively, if the exact element cannot be found.\n\t */\n\tfunction recursiveSearch(aLow, aHigh, aNeedle, aHaystack, aCompare, aBias) {\n\t  // This function terminates when one of the following is true:\n\t  //\n\t  //   1. We find the exact element we are looking for.\n\t  //\n\t  //   2. We did not find the exact element, but we can return the index of\n\t  //      the next-closest element.\n\t  //\n\t  //   3. We did not find the exact element, and there is no next-closest\n\t  //      element than the one we are searching for, so we return -1.\n\t  var mid = Math.floor((aHigh - aLow) / 2) + aLow;\n\t  var cmp = aCompare(aNeedle, aHaystack[mid], true);\n\t  if (cmp === 0) {\n\t    // Found the element we are looking for.\n\t    return mid;\n\t  }\n\t  else if (cmp > 0) {\n\t    // Our needle is greater than aHaystack[mid].\n\t    if (aHigh - mid > 1) {\n\t      // The element is in the upper half.\n\t      return recursiveSearch(mid, aHigh, aNeedle, aHaystack, aCompare, aBias);\n\t    }\n\t\n\t    // The exact needle element was not found in this haystack. Determine if\n\t    // we are in termination case (3) or (2) and return the appropriate thing.\n\t    if (aBias == exports.LEAST_UPPER_BOUND) {\n\t      return aHigh < aHaystack.length ? aHigh : -1;\n\t    } else {\n\t      return mid;\n\t    }\n\t  }\n\t  else {\n\t    // Our needle is less than aHaystack[mid].\n\t    if (mid - aLow > 1) {\n\t      // The element is in the lower half.\n\t      return recursiveSearch(aLow, mid, aNeedle, aHaystack, aCompare, aBias);\n\t    }\n\t\n\t    // we are in termination case (3) or (2) and return the appropriate thing.\n\t    if (aBias == exports.LEAST_UPPER_BOUND) {\n\t      return mid;\n\t    } else {\n\t      return aLow < 0 ? -1 : aLow;\n\t    }\n\t  }\n\t}\n\t\n\t/**\n\t * This is an implementation of binary search which will always try and return\n\t * the index of the closest element if there is no exact hit. This is because\n\t * mappings between original and generated line/col pairs are single points,\n\t * and there is an implicit region between each of them, so a miss just means\n\t * that you aren't on the very start of a region.\n\t *\n\t * @param aNeedle The element you are looking for.\n\t * @param aHaystack The array that is being searched.\n\t * @param aCompare A function which takes the needle and an element in the\n\t *     array and returns -1, 0, or 1 depending on whether the needle is less\n\t *     than, equal to, or greater than the element, respectively.\n\t * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n\t *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n\t *     closest element that is smaller than or greater than the one we are\n\t *     searching for, respectively, if the exact element cannot be found.\n\t *     Defaults to 'binarySearch.GREATEST_LOWER_BOUND'.\n\t */\n\texports.search = function search(aNeedle, aHaystack, aCompare, aBias) {\n\t  if (aHaystack.length === 0) {\n\t    return -1;\n\t  }\n\t\n\t  var index = recursiveSearch(-1, aHaystack.length, aNeedle, aHaystack,\n\t                              aCompare, aBias || exports.GREATEST_LOWER_BOUND);\n\t  if (index < 0) {\n\t    return -1;\n\t  }\n\t\n\t  // We have found either the exact element, or the next-closest element than\n\t  // the one we are searching for. However, there may be more than one such\n\t  // element. Make sure we always return the smallest of these.\n\t  while (index - 1 >= 0) {\n\t    if (aCompare(aHaystack[index], aHaystack[index - 1], true) !== 0) {\n\t      break;\n\t    }\n\t    --index;\n\t  }\n\t\n\t  return index;\n\t};\n\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports) {\n\n\t/* -*- Mode: js; js-indent-level: 2; -*- */\n\t/*\n\t * Copyright 2011 Mozilla Foundation and contributors\n\t * Licensed under the New BSD license. See LICENSE or:\n\t * http://opensource.org/licenses/BSD-3-Clause\n\t */\n\t\n\t// It turns out that some (most?) JavaScript engines don't self-host\n\t// `Array.prototype.sort`. This makes sense because C++ will likely remain\n\t// faster than JS when doing raw CPU-intensive sorting. However, when using a\n\t// custom comparator function, calling back and forth between the VM's C++ and\n\t// JIT'd JS is rather slow *and* loses JIT type information, resulting in\n\t// worse generated code for the comparator function than would be optimal. In\n\t// fact, when sorting with a comparator, these costs outweigh the benefits of\n\t// sorting in C++. By using our own JS-implemented Quick Sort (below), we get\n\t// a ~3500ms mean speed-up in `bench/bench.html`.\n\t\n\t/**\n\t * Swap the elements indexed by `x` and `y` in the array `ary`.\n\t *\n\t * @param {Array} ary\n\t *        The array.\n\t * @param {Number} x\n\t *        The index of the first item.\n\t * @param {Number} y\n\t *        The index of the second item.\n\t */\n\tfunction swap(ary, x, y) {\n\t  var temp = ary[x];\n\t  ary[x] = ary[y];\n\t  ary[y] = temp;\n\t}\n\t\n\t/**\n\t * Returns a random integer within the range `low .. high` inclusive.\n\t *\n\t * @param {Number} low\n\t *        The lower bound on the range.\n\t * @param {Number} high\n\t *        The upper bound on the range.\n\t */\n\tfunction randomIntInRange(low, high) {\n\t  return Math.round(low + (Math.random() * (high - low)));\n\t}\n\t\n\t/**\n\t * The Quick Sort algorithm.\n\t *\n\t * @param {Array} ary\n\t *        An array to sort.\n\t * @param {function} comparator\n\t *        Function to use to compare two items.\n\t * @param {Number} p\n\t *        Start index of the array\n\t * @param {Number} r\n\t *        End index of the array\n\t */\n\tfunction doQuickSort(ary, comparator, p, r) {\n\t  // If our lower bound is less than our upper bound, we (1) partition the\n\t  // array into two pieces and (2) recurse on each half. If it is not, this is\n\t  // the empty array and our base case.\n\t\n\t  if (p < r) {\n\t    // (1) Partitioning.\n\t    //\n\t    // The partitioning chooses a pivot between `p` and `r` and moves all\n\t    // elements that are less than or equal to the pivot to the before it, and\n\t    // all the elements that are greater than it after it. The effect is that\n\t    // once partition is done, the pivot is in the exact place it will be when\n\t    // the array is put in sorted order, and it will not need to be moved\n\t    // again. This runs in O(n) time.\n\t\n\t    // Always choose a random pivot so that an input array which is reverse\n\t    // sorted does not cause O(n^2) running time.\n\t    var pivotIndex = randomIntInRange(p, r);\n\t    var i = p - 1;\n\t\n\t    swap(ary, pivotIndex, r);\n\t    var pivot = ary[r];\n\t\n\t    // Immediately after `j` is incremented in this loop, the following hold\n\t    // true:\n\t    //\n\t    //   * Every element in `ary[p .. i]` is less than or equal to the pivot.\n\t    //\n\t    //   * Every element in `ary[i+1 .. j-1]` is greater than the pivot.\n\t    for (var j = p; j < r; j++) {\n\t      if (comparator(ary[j], pivot) <= 0) {\n\t        i += 1;\n\t        swap(ary, i, j);\n\t      }\n\t    }\n\t\n\t    swap(ary, i + 1, j);\n\t    var q = i + 1;\n\t\n\t    // (2) Recurse on each half.\n\t\n\t    doQuickSort(ary, comparator, p, q - 1);\n\t    doQuickSort(ary, comparator, q + 1, r);\n\t  }\n\t}\n\t\n\t/**\n\t * Sort the given array in-place with the given comparator function.\n\t *\n\t * @param {Array} ary\n\t *        An array to sort.\n\t * @param {function} comparator\n\t *        Function to use to compare two items.\n\t */\n\texports.quickSort = function (ary, comparator) {\n\t  doQuickSort(ary, comparator, 0, ary.length - 1);\n\t};\n\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* -*- Mode: js; js-indent-level: 2; -*- */\n\t/*\n\t * Copyright 2011 Mozilla Foundation and contributors\n\t * Licensed under the New BSD license. See LICENSE or:\n\t * http://opensource.org/licenses/BSD-3-Clause\n\t */\n\t\n\tvar SourceMapGenerator = __webpack_require__(1).SourceMapGenerator;\n\tvar util = __webpack_require__(4);\n\t\n\t// Matches a Windows-style `\\r\\n` newline or a `\\n` newline used by all other\n\t// operating systems these days (capturing the result).\n\tvar REGEX_NEWLINE = /(\\r?\\n)/;\n\t\n\t// Newline character code for charCodeAt() comparisons\n\tvar NEWLINE_CODE = 10;\n\t\n\t// Private symbol for identifying `SourceNode`s when multiple versions of\n\t// the source-map library are loaded. This MUST NOT CHANGE across\n\t// versions!\n\tvar isSourceNode = \"$$$isSourceNode$$$\";\n\t\n\t/**\n\t * SourceNodes provide a way to abstract over interpolating/concatenating\n\t * snippets of generated JavaScript source code while maintaining the line and\n\t * column information associated with the original source code.\n\t *\n\t * @param aLine The original line number.\n\t * @param aColumn The original column number.\n\t * @param aSource The original source's filename.\n\t * @param aChunks Optional. An array of strings which are snippets of\n\t *        generated JS, or other SourceNodes.\n\t * @param aName The original identifier.\n\t */\n\tfunction SourceNode(aLine, aColumn, aSource, aChunks, aName) {\n\t  this.children = [];\n\t  this.sourceContents = {};\n\t  this.line = aLine == null ? null : aLine;\n\t  this.column = aColumn == null ? null : aColumn;\n\t  this.source = aSource == null ? null : aSource;\n\t  this.name = aName == null ? null : aName;\n\t  this[isSourceNode] = true;\n\t  if (aChunks != null) this.add(aChunks);\n\t}\n\t\n\t/**\n\t * Creates a SourceNode from generated code and a SourceMapConsumer.\n\t *\n\t * @param aGeneratedCode The generated code\n\t * @param aSourceMapConsumer The SourceMap for the generated code\n\t * @param aRelativePath Optional. The path that relative sources in the\n\t *        SourceMapConsumer should be relative to.\n\t */\n\tSourceNode.fromStringWithSourceMap =\n\t  function SourceNode_fromStringWithSourceMap(aGeneratedCode, aSourceMapConsumer, aRelativePath) {\n\t    // The SourceNode we want to fill with the generated code\n\t    // and the SourceMap\n\t    var node = new SourceNode();\n\t\n\t    // All even indices of this array are one line of the generated code,\n\t    // while all odd indices are the newlines between two adjacent lines\n\t    // (since `REGEX_NEWLINE` captures its match).\n\t    // Processed fragments are accessed by calling `shiftNextLine`.\n\t    var remainingLines = aGeneratedCode.split(REGEX_NEWLINE);\n\t    var remainingLinesIndex = 0;\n\t    var shiftNextLine = function() {\n\t      var lineContents = getNextLine();\n\t      // The last line of a file might not have a newline.\n\t      var newLine = getNextLine() || \"\";\n\t      return lineContents + newLine;\n\t\n\t      function getNextLine() {\n\t        return remainingLinesIndex < remainingLines.length ?\n\t            remainingLines[remainingLinesIndex++] : undefined;\n\t      }\n\t    };\n\t\n\t    // We need to remember the position of \"remainingLines\"\n\t    var lastGeneratedLine = 1, lastGeneratedColumn = 0;\n\t\n\t    // The generate SourceNodes we need a code range.\n\t    // To extract it current and last mapping is used.\n\t    // Here we store the last mapping.\n\t    var lastMapping = null;\n\t\n\t    aSourceMapConsumer.eachMapping(function (mapping) {\n\t      if (lastMapping !== null) {\n\t        // We add the code from \"lastMapping\" to \"mapping\":\n\t        // First check if there is a new line in between.\n\t        if (lastGeneratedLine < mapping.generatedLine) {\n\t          // Associate first line with \"lastMapping\"\n\t          addMappingWithCode(lastMapping, shiftNextLine());\n\t          lastGeneratedLine++;\n\t          lastGeneratedColumn = 0;\n\t          // The remaining code is added without mapping\n\t        } else {\n\t          // There is no new line in between.\n\t          // Associate the code between \"lastGeneratedColumn\" and\n\t          // \"mapping.generatedColumn\" with \"lastMapping\"\n\t          var nextLine = remainingLines[remainingLinesIndex] || '';\n\t          var code = nextLine.substr(0, mapping.generatedColumn -\n\t                                        lastGeneratedColumn);\n\t          remainingLines[remainingLinesIndex] = nextLine.substr(mapping.generatedColumn -\n\t                                              lastGeneratedColumn);\n\t          lastGeneratedColumn = mapping.generatedColumn;\n\t          addMappingWithCode(lastMapping, code);\n\t          // No more remaining code, continue\n\t          lastMapping = mapping;\n\t          return;\n\t        }\n\t      }\n\t      // We add the generated code until the first mapping\n\t      // to the SourceNode without any mapping.\n\t      // Each line is added as separate string.\n\t      while (lastGeneratedLine < mapping.generatedLine) {\n\t        node.add(shiftNextLine());\n\t        lastGeneratedLine++;\n\t      }\n\t      if (lastGeneratedColumn < mapping.generatedColumn) {\n\t        var nextLine = remainingLines[remainingLinesIndex] || '';\n\t        node.add(nextLine.substr(0, mapping.generatedColumn));\n\t        remainingLines[remainingLinesIndex] = nextLine.substr(mapping.generatedColumn);\n\t        lastGeneratedColumn = mapping.generatedColumn;\n\t      }\n\t      lastMapping = mapping;\n\t    }, this);\n\t    // We have processed all mappings.\n\t    if (remainingLinesIndex < remainingLines.length) {\n\t      if (lastMapping) {\n\t        // Associate the remaining code in the current line with \"lastMapping\"\n\t        addMappingWithCode(lastMapping, shiftNextLine());\n\t      }\n\t      // and add the remaining lines without any mapping\n\t      node.add(remainingLines.splice(remainingLinesIndex).join(\"\"));\n\t    }\n\t\n\t    // Copy sourcesContent into SourceNode\n\t    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n\t      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n\t      if (content != null) {\n\t        if (aRelativePath != null) {\n\t          sourceFile = util.join(aRelativePath, sourceFile);\n\t        }\n\t        node.setSourceContent(sourceFile, content);\n\t      }\n\t    });\n\t\n\t    return node;\n\t\n\t    function addMappingWithCode(mapping, code) {\n\t      if (mapping === null || mapping.source === undefined) {\n\t        node.add(code);\n\t      } else {\n\t        var source = aRelativePath\n\t          ? util.join(aRelativePath, mapping.source)\n\t          : mapping.source;\n\t        node.add(new SourceNode(mapping.originalLine,\n\t                                mapping.originalColumn,\n\t                                source,\n\t                                code,\n\t                                mapping.name));\n\t      }\n\t    }\n\t  };\n\t\n\t/**\n\t * Add a chunk of generated JS to this source node.\n\t *\n\t * @param aChunk A string snippet of generated JS code, another instance of\n\t *        SourceNode, or an array where each member is one of those things.\n\t */\n\tSourceNode.prototype.add = function SourceNode_add(aChunk) {\n\t  if (Array.isArray(aChunk)) {\n\t    aChunk.forEach(function (chunk) {\n\t      this.add(chunk);\n\t    }, this);\n\t  }\n\t  else if (aChunk[isSourceNode] || typeof aChunk === \"string\") {\n\t    if (aChunk) {\n\t      this.children.push(aChunk);\n\t    }\n\t  }\n\t  else {\n\t    throw new TypeError(\n\t      \"Expected a SourceNode, string, or an array of SourceNodes and strings. Got \" + aChunk\n\t    );\n\t  }\n\t  return this;\n\t};\n\t\n\t/**\n\t * Add a chunk of generated JS to the beginning of this source node.\n\t *\n\t * @param aChunk A string snippet of generated JS code, another instance of\n\t *        SourceNode, or an array where each member is one of those things.\n\t */\n\tSourceNode.prototype.prepend = function SourceNode_prepend(aChunk) {\n\t  if (Array.isArray(aChunk)) {\n\t    for (var i = aChunk.length-1; i >= 0; i--) {\n\t      this.prepend(aChunk[i]);\n\t    }\n\t  }\n\t  else if (aChunk[isSourceNode] || typeof aChunk === \"string\") {\n\t    this.children.unshift(aChunk);\n\t  }\n\t  else {\n\t    throw new TypeError(\n\t      \"Expected a SourceNode, string, or an array of SourceNodes and strings. Got \" + aChunk\n\t    );\n\t  }\n\t  return this;\n\t};\n\t\n\t/**\n\t * Walk over the tree of JS snippets in this node and its children. The\n\t * walking function is called once for each snippet of JS and is passed that\n\t * snippet and the its original associated source's line/column location.\n\t *\n\t * @param aFn The traversal function.\n\t */\n\tSourceNode.prototype.walk = function SourceNode_walk(aFn) {\n\t  var chunk;\n\t  for (var i = 0, len = this.children.length; i < len; i++) {\n\t    chunk = this.children[i];\n\t    if (chunk[isSourceNode]) {\n\t      chunk.walk(aFn);\n\t    }\n\t    else {\n\t      if (chunk !== '') {\n\t        aFn(chunk, { source: this.source,\n\t                     line: this.line,\n\t                     column: this.column,\n\t                     name: this.name });\n\t      }\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Like `String.prototype.join` except for SourceNodes. Inserts `aStr` between\n\t * each of `this.children`.\n\t *\n\t * @param aSep The separator.\n\t */\n\tSourceNode.prototype.join = function SourceNode_join(aSep) {\n\t  var newChildren;\n\t  var i;\n\t  var len = this.children.length;\n\t  if (len > 0) {\n\t    newChildren = [];\n\t    for (i = 0; i < len-1; i++) {\n\t      newChildren.push(this.children[i]);\n\t      newChildren.push(aSep);\n\t    }\n\t    newChildren.push(this.children[i]);\n\t    this.children = newChildren;\n\t  }\n\t  return this;\n\t};\n\t\n\t/**\n\t * Call String.prototype.replace on the very right-most source snippet. Useful\n\t * for trimming whitespace from the end of a source node, etc.\n\t *\n\t * @param aPattern The pattern to replace.\n\t * @param aReplacement The thing to replace the pattern with.\n\t */\n\tSourceNode.prototype.replaceRight = function SourceNode_replaceRight(aPattern, aReplacement) {\n\t  var lastChild = this.children[this.children.length - 1];\n\t  if (lastChild[isSourceNode]) {\n\t    lastChild.replaceRight(aPattern, aReplacement);\n\t  }\n\t  else if (typeof lastChild === 'string') {\n\t    this.children[this.children.length - 1] = lastChild.replace(aPattern, aReplacement);\n\t  }\n\t  else {\n\t    this.children.push(''.replace(aPattern, aReplacement));\n\t  }\n\t  return this;\n\t};\n\t\n\t/**\n\t * Set the source content for a source file. This will be added to the SourceMapGenerator\n\t * in the sourcesContent field.\n\t *\n\t * @param aSourceFile The filename of the source file\n\t * @param aSourceContent The content of the source file\n\t */\n\tSourceNode.prototype.setSourceContent =\n\t  function SourceNode_setSourceContent(aSourceFile, aSourceContent) {\n\t    this.sourceContents[util.toSetString(aSourceFile)] = aSourceContent;\n\t  };\n\t\n\t/**\n\t * Walk over the tree of SourceNodes. The walking function is called for each\n\t * source file content and is passed the filename and source content.\n\t *\n\t * @param aFn The traversal function.\n\t */\n\tSourceNode.prototype.walkSourceContents =\n\t  function SourceNode_walkSourceContents(aFn) {\n\t    for (var i = 0, len = this.children.length; i < len; i++) {\n\t      if (this.children[i][isSourceNode]) {\n\t        this.children[i].walkSourceContents(aFn);\n\t      }\n\t    }\n\t\n\t    var sources = Object.keys(this.sourceContents);\n\t    for (var i = 0, len = sources.length; i < len; i++) {\n\t      aFn(util.fromSetString(sources[i]), this.sourceContents[sources[i]]);\n\t    }\n\t  };\n\t\n\t/**\n\t * Return the string representation of this source node. Walks over the tree\n\t * and concatenates all the various snippets together to one string.\n\t */\n\tSourceNode.prototype.toString = function SourceNode_toString() {\n\t  var str = \"\";\n\t  this.walk(function (chunk) {\n\t    str += chunk;\n\t  });\n\t  return str;\n\t};\n\t\n\t/**\n\t * Returns the string representation of this source node along with a source\n\t * map.\n\t */\n\tSourceNode.prototype.toStringWithSourceMap = function SourceNode_toStringWithSourceMap(aArgs) {\n\t  var generated = {\n\t    code: \"\",\n\t    line: 1,\n\t    column: 0\n\t  };\n\t  var map = new SourceMapGenerator(aArgs);\n\t  var sourceMappingActive = false;\n\t  var lastOriginalSource = null;\n\t  var lastOriginalLine = null;\n\t  var lastOriginalColumn = null;\n\t  var lastOriginalName = null;\n\t  this.walk(function (chunk, original) {\n\t    generated.code += chunk;\n\t    if (original.source !== null\n\t        && original.line !== null\n\t        && original.column !== null) {\n\t      if(lastOriginalSource !== original.source\n\t         || lastOriginalLine !== original.line\n\t         || lastOriginalColumn !== original.column\n\t         || lastOriginalName !== original.name) {\n\t        map.addMapping({\n\t          source: original.source,\n\t          original: {\n\t            line: original.line,\n\t            column: original.column\n\t          },\n\t          generated: {\n\t            line: generated.line,\n\t            column: generated.column\n\t          },\n\t          name: original.name\n\t        });\n\t      }\n\t      lastOriginalSource = original.source;\n\t      lastOriginalLine = original.line;\n\t      lastOriginalColumn = original.column;\n\t      lastOriginalName = original.name;\n\t      sourceMappingActive = true;\n\t    } else if (sourceMappingActive) {\n\t      map.addMapping({\n\t        generated: {\n\t          line: generated.line,\n\t          column: generated.column\n\t        }\n\t      });\n\t      lastOriginalSource = null;\n\t      sourceMappingActive = false;\n\t    }\n\t    for (var idx = 0, length = chunk.length; idx < length; idx++) {\n\t      if (chunk.charCodeAt(idx) === NEWLINE_CODE) {\n\t        generated.line++;\n\t        generated.column = 0;\n\t        // Mappings end at eol\n\t        if (idx + 1 === length) {\n\t          lastOriginalSource = null;\n\t          sourceMappingActive = false;\n\t        } else if (sourceMappingActive) {\n\t          map.addMapping({\n\t            source: original.source,\n\t            original: {\n\t              line: original.line,\n\t              column: original.column\n\t            },\n\t            generated: {\n\t              line: generated.line,\n\t              column: generated.column\n\t            },\n\t            name: original.name\n\t          });\n\t        }\n\t      } else {\n\t        generated.column++;\n\t      }\n\t    }\n\t  });\n\t  this.walkSourceContents(function (sourceFile, sourceContent) {\n\t    map.setSourceContent(sourceFile, sourceContent);\n\t  });\n\t\n\t  return { code: generated.code, map: map };\n\t};\n\t\n\texports.SourceNode = SourceNode;\n\n\n/***/ })\n/******/ ])\n});\n;\n\n\n// WEBPACK FOOTER //\n// source-map.min.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId])\n \t\t\treturn installedModules[moduleId].exports;\n\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\texports: {},\n \t\t\tid: moduleId,\n \t\t\tloaded: false\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.loaded = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(0);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 0fd5815da764db5fb9fe", "/*\n * Copyright 2009-2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE.txt or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\nexports.SourceMapGenerator = require('./lib/source-map-generator').SourceMapGenerator;\nexports.SourceMapConsumer = require('./lib/source-map-consumer').SourceMapConsumer;\nexports.SourceNode = require('./lib/source-node').SourceNode;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./source-map.js\n// module id = 0\n// module chunks = 0", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar base64VLQ = require('./base64-vlq');\nvar util = require('./util');\nvar ArraySet = require('./array-set').ArraySet;\nvar MappingList = require('./mapping-list').MappingList;\n\n/**\n * An instance of the SourceMapGenerator represents a source map which is\n * being built incrementally. You may pass an object with the following\n * properties:\n *\n *   - file: The filename of the generated source.\n *   - sourceRoot: A root for all relative URLs in this source map.\n */\nfunction SourceMapGenerator(aArgs) {\n  if (!aArgs) {\n    aArgs = {};\n  }\n  this._file = util.getArg(aArgs, 'file', null);\n  this._sourceRoot = util.getArg(aArgs, 'sourceRoot', null);\n  this._skipValidation = util.getArg(aArgs, 'skipValidation', false);\n  this._sources = new ArraySet();\n  this._names = new ArraySet();\n  this._mappings = new MappingList();\n  this._sourcesContents = null;\n}\n\nSourceMapGenerator.prototype._version = 3;\n\n/**\n * Creates a new SourceMapGenerator based on a SourceMapConsumer\n *\n * @param aSourceMapConsumer The SourceMap.\n */\nSourceMapGenerator.fromSourceMap =\n  function SourceMapGenerator_fromSourceMap(aSourceMapConsumer) {\n    var sourceRoot = aSourceMapConsumer.sourceRoot;\n    var generator = new SourceMapGenerator({\n      file: aSourceMapConsumer.file,\n      sourceRoot: sourceRoot\n    });\n    aSourceMapConsumer.eachMapping(function (mapping) {\n      var newMapping = {\n        generated: {\n          line: mapping.generatedLine,\n          column: mapping.generatedColumn\n        }\n      };\n\n      if (mapping.source != null) {\n        newMapping.source = mapping.source;\n        if (sourceRoot != null) {\n          newMapping.source = util.relative(sourceRoot, newMapping.source);\n        }\n\n        newMapping.original = {\n          line: mapping.originalLine,\n          column: mapping.originalColumn\n        };\n\n        if (mapping.name != null) {\n          newMapping.name = mapping.name;\n        }\n      }\n\n      generator.addMapping(newMapping);\n    });\n    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n      var sourceRelative = sourceFile;\n      if (sourceRoot !== null) {\n        sourceRelative = util.relative(sourceRoot, sourceFile);\n      }\n\n      if (!generator._sources.has(sourceRelative)) {\n        generator._sources.add(sourceRelative);\n      }\n\n      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n      if (content != null) {\n        generator.setSourceContent(sourceFile, content);\n      }\n    });\n    return generator;\n  };\n\n/**\n * Add a single mapping from original source line and column to the generated\n * source's line and column for this source map being created. The mapping\n * object should have the following properties:\n *\n *   - generated: An object with the generated line and column positions.\n *   - original: An object with the original line and column positions.\n *   - source: The original source file (relative to the sourceRoot).\n *   - name: An optional original token name for this mapping.\n */\nSourceMapGenerator.prototype.addMapping =\n  function SourceMapGenerator_addMapping(aArgs) {\n    var generated = util.getArg(aArgs, 'generated');\n    var original = util.getArg(aArgs, 'original', null);\n    var source = util.getArg(aArgs, 'source', null);\n    var name = util.getArg(aArgs, 'name', null);\n\n    if (!this._skipValidation) {\n      this._validateMapping(generated, original, source, name);\n    }\n\n    if (source != null) {\n      source = String(source);\n      if (!this._sources.has(source)) {\n        this._sources.add(source);\n      }\n    }\n\n    if (name != null) {\n      name = String(name);\n      if (!this._names.has(name)) {\n        this._names.add(name);\n      }\n    }\n\n    this._mappings.add({\n      generatedLine: generated.line,\n      generatedColumn: generated.column,\n      originalLine: original != null && original.line,\n      originalColumn: original != null && original.column,\n      source: source,\n      name: name\n    });\n  };\n\n/**\n * Set the source content for a source file.\n */\nSourceMapGenerator.prototype.setSourceContent =\n  function SourceMapGenerator_setSourceContent(aSourceFile, aSourceContent) {\n    var source = aSourceFile;\n    if (this._sourceRoot != null) {\n      source = util.relative(this._sourceRoot, source);\n    }\n\n    if (aSourceContent != null) {\n      // Add the source content to the _sourcesContents map.\n      // Create a new _sourcesContents map if the property is null.\n      if (!this._sourcesContents) {\n        this._sourcesContents = Object.create(null);\n      }\n      this._sourcesContents[util.toSetString(source)] = aSourceContent;\n    } else if (this._sourcesContents) {\n      // Remove the source file from the _sourcesContents map.\n      // If the _sourcesContents map is empty, set the property to null.\n      delete this._sourcesContents[util.toSetString(source)];\n      if (Object.keys(this._sourcesContents).length === 0) {\n        this._sourcesContents = null;\n      }\n    }\n  };\n\n/**\n * Applies the mappings of a sub-source-map for a specific source file to the\n * source map being generated. Each mapping to the supplied source file is\n * rewritten using the supplied source map. Note: The resolution for the\n * resulting mappings is the minimium of this map and the supplied map.\n *\n * @param aSourceMapConsumer The source map to be applied.\n * @param aSourceFile Optional. The filename of the source file.\n *        If omitted, SourceMapConsumer's file property will be used.\n * @param aSourceMapPath Optional. The dirname of the path to the source map\n *        to be applied. If relative, it is relative to the SourceMapConsumer.\n *        This parameter is needed when the two source maps aren't in the same\n *        directory, and the source map to be applied contains relative source\n *        paths. If so, those relative source paths need to be rewritten\n *        relative to the SourceMapGenerator.\n */\nSourceMapGenerator.prototype.applySourceMap =\n  function SourceMapGenerator_applySourceMap(aSourceMapConsumer, aSourceFile, aSourceMapPath) {\n    var sourceFile = aSourceFile;\n    // If aSourceFile is omitted, we will use the file property of the SourceMap\n    if (aSourceFile == null) {\n      if (aSourceMapConsumer.file == null) {\n        throw new Error(\n          'SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, ' +\n          'or the source map\\'s \"file\" property. Both were omitted.'\n        );\n      }\n      sourceFile = aSourceMapConsumer.file;\n    }\n    var sourceRoot = this._sourceRoot;\n    // Make \"sourceFile\" relative if an absolute Url is passed.\n    if (sourceRoot != null) {\n      sourceFile = util.relative(sourceRoot, sourceFile);\n    }\n    // Applying the SourceMap can add and remove items from the sources and\n    // the names array.\n    var newSources = new ArraySet();\n    var newNames = new ArraySet();\n\n    // Find mappings for the \"sourceFile\"\n    this._mappings.unsortedForEach(function (mapping) {\n      if (mapping.source === sourceFile && mapping.originalLine != null) {\n        // Check if it can be mapped by the source map, then update the mapping.\n        var original = aSourceMapConsumer.originalPositionFor({\n          line: mapping.originalLine,\n          column: mapping.originalColumn\n        });\n        if (original.source != null) {\n          // Copy mapping\n          mapping.source = original.source;\n          if (aSourceMapPath != null) {\n            mapping.source = util.join(aSourceMapPath, mapping.source)\n          }\n          if (sourceRoot != null) {\n            mapping.source = util.relative(sourceRoot, mapping.source);\n          }\n          mapping.originalLine = original.line;\n          mapping.originalColumn = original.column;\n          if (original.name != null) {\n            mapping.name = original.name;\n          }\n        }\n      }\n\n      var source = mapping.source;\n      if (source != null && !newSources.has(source)) {\n        newSources.add(source);\n      }\n\n      var name = mapping.name;\n      if (name != null && !newNames.has(name)) {\n        newNames.add(name);\n      }\n\n    }, this);\n    this._sources = newSources;\n    this._names = newNames;\n\n    // Copy sourcesContents of applied map.\n    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n      if (content != null) {\n        if (aSourceMapPath != null) {\n          sourceFile = util.join(aSourceMapPath, sourceFile);\n        }\n        if (sourceRoot != null) {\n          sourceFile = util.relative(sourceRoot, sourceFile);\n        }\n        this.setSourceContent(sourceFile, content);\n      }\n    }, this);\n  };\n\n/**\n * A mapping can have one of the three levels of data:\n *\n *   1. Just the generated position.\n *   2. The Generated position, original position, and original source.\n *   3. Generated and original position, original source, as well as a name\n *      token.\n *\n * To maintain consistency, we validate that any new mapping being added falls\n * in to one of these categories.\n */\nSourceMapGenerator.prototype._validateMapping =\n  function SourceMapGenerator_validateMapping(aGenerated, aOriginal, aSource,\n                                              aName) {\n    // When aOriginal is truthy but has empty values for .line and .column,\n    // it is most likely a programmer error. In this case we throw a very\n    // specific error message to try to guide them the right way.\n    // For example: https://github.com/Polymer/polymer-bundler/pull/519\n    if (aOriginal && typeof aOriginal.line !== 'number' && typeof aOriginal.column !== 'number') {\n        throw new Error(\n            'original.line and original.column are not numbers -- you probably meant to omit ' +\n            'the original mapping entirely and only map the generated position. If so, pass ' +\n            'null for the original mapping instead of an object with empty or null values.'\n        );\n    }\n\n    if (aGenerated && 'line' in aGenerated && 'column' in aGenerated\n        && aGenerated.line > 0 && aGenerated.column >= 0\n        && !aOriginal && !aSource && !aName) {\n      // Case 1.\n      return;\n    }\n    else if (aGenerated && 'line' in aGenerated && 'column' in aGenerated\n             && aOriginal && 'line' in aOriginal && 'column' in aOriginal\n             && aGenerated.line > 0 && aGenerated.column >= 0\n             && aOriginal.line > 0 && aOriginal.column >= 0\n             && aSource) {\n      // Cases 2 and 3.\n      return;\n    }\n    else {\n      throw new Error('Invalid mapping: ' + JSON.stringify({\n        generated: aGenerated,\n        source: aSource,\n        original: aOriginal,\n        name: aName\n      }));\n    }\n  };\n\n/**\n * Serialize the accumulated mappings in to the stream of base 64 VLQs\n * specified by the source map format.\n */\nSourceMapGenerator.prototype._serializeMappings =\n  function SourceMapGenerator_serializeMappings() {\n    var previousGeneratedColumn = 0;\n    var previousGeneratedLine = 1;\n    var previousOriginalColumn = 0;\n    var previousOriginalLine = 0;\n    var previousName = 0;\n    var previousSource = 0;\n    var result = '';\n    var next;\n    var mapping;\n    var nameIdx;\n    var sourceIdx;\n\n    var mappings = this._mappings.toArray();\n    for (var i = 0, len = mappings.length; i < len; i++) {\n      mapping = mappings[i];\n      next = ''\n\n      if (mapping.generatedLine !== previousGeneratedLine) {\n        previousGeneratedColumn = 0;\n        while (mapping.generatedLine !== previousGeneratedLine) {\n          next += ';';\n          previousGeneratedLine++;\n        }\n      }\n      else {\n        if (i > 0) {\n          if (!util.compareByGeneratedPositionsInflated(mapping, mappings[i - 1])) {\n            continue;\n          }\n          next += ',';\n        }\n      }\n\n      next += base64VLQ.encode(mapping.generatedColumn\n                                 - previousGeneratedColumn);\n      previousGeneratedColumn = mapping.generatedColumn;\n\n      if (mapping.source != null) {\n        sourceIdx = this._sources.indexOf(mapping.source);\n        next += base64VLQ.encode(sourceIdx - previousSource);\n        previousSource = sourceIdx;\n\n        // lines are stored 0-based in SourceMap spec version 3\n        next += base64VLQ.encode(mapping.originalLine - 1\n                                   - previousOriginalLine);\n        previousOriginalLine = mapping.originalLine - 1;\n\n        next += base64VLQ.encode(mapping.originalColumn\n                                   - previousOriginalColumn);\n        previousOriginalColumn = mapping.originalColumn;\n\n        if (mapping.name != null) {\n          nameIdx = this._names.indexOf(mapping.name);\n          next += base64VLQ.encode(nameIdx - previousName);\n          previousName = nameIdx;\n        }\n      }\n\n      result += next;\n    }\n\n    return result;\n  };\n\nSourceMapGenerator.prototype._generateSourcesContent =\n  function SourceMapGenerator_generateSourcesContent(aSources, aSourceRoot) {\n    return aSources.map(function (source) {\n      if (!this._sourcesContents) {\n        return null;\n      }\n      if (aSourceRoot != null) {\n        source = util.relative(aSourceRoot, source);\n      }\n      var key = util.toSetString(source);\n      return Object.prototype.hasOwnProperty.call(this._sourcesContents, key)\n        ? this._sourcesContents[key]\n        : null;\n    }, this);\n  };\n\n/**\n * Externalize the source map.\n */\nSourceMapGenerator.prototype.toJSON =\n  function SourceMapGenerator_toJSON() {\n    var map = {\n      version: this._version,\n      sources: this._sources.toArray(),\n      names: this._names.toArray(),\n      mappings: this._serializeMappings()\n    };\n    if (this._file != null) {\n      map.file = this._file;\n    }\n    if (this._sourceRoot != null) {\n      map.sourceRoot = this._sourceRoot;\n    }\n    if (this._sourcesContents) {\n      map.sourcesContent = this._generateSourcesContent(map.sources, map.sourceRoot);\n    }\n\n    return map;\n  };\n\n/**\n * Render the source map being generated to a string.\n */\nSourceMapGenerator.prototype.toString =\n  function SourceMapGenerator_toString() {\n    return JSON.stringify(this.toJSON());\n  };\n\nexports.SourceMapGenerator = SourceMapGenerator;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/source-map-generator.js\n// module id = 1\n// module chunks = 0", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n *\n * Based on the Base 64 VLQ implementation in Closure Compiler:\n * https://code.google.com/p/closure-compiler/source/browse/trunk/src/com/google/debugging/sourcemap/Base64VLQ.java\n *\n * Copyright 2011 The Closure Compiler Authors. All rights reserved.\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are\n * met:\n *\n *  * Redistributions of source code must retain the above copyright\n *    notice, this list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above\n *    copyright notice, this list of conditions and the following\n *    disclaimer in the documentation and/or other materials provided\n *    with the distribution.\n *  * Neither the name of Google Inc. nor the names of its\n *    contributors may be used to endorse or promote products derived\n *    from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n * \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar base64 = require('./base64');\n\n// A single base 64 digit can contain 6 bits of data. For the base 64 variable\n// length quantities we use in the source map spec, the first bit is the sign,\n// the next four bits are the actual value, and the 6th bit is the\n// continuation bit. The continuation bit tells us whether there are more\n// digits in this value following this digit.\n//\n//   Continuation\n//   |    Sign\n//   |    |\n//   V    V\n//   101011\n\nvar VLQ_BASE_SHIFT = 5;\n\n// binary: 100000\nvar VLQ_BASE = 1 << VLQ_BASE_SHIFT;\n\n// binary: 011111\nvar VLQ_BASE_MASK = VLQ_BASE - 1;\n\n// binary: 100000\nvar VLQ_CONTINUATION_BIT = VLQ_BASE;\n\n/**\n * Converts from a two-complement value to a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   1 becomes 2 (10 binary), -1 becomes 3 (11 binary)\n *   2 becomes 4 (100 binary), -2 becomes 5 (101 binary)\n */\nfunction toVLQSigned(aValue) {\n  return aValue < 0\n    ? ((-aValue) << 1) + 1\n    : (aValue << 1) + 0;\n}\n\n/**\n * Converts to a two-complement value from a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   2 (10 binary) becomes 1, 3 (11 binary) becomes -1\n *   4 (100 binary) becomes 2, 5 (101 binary) becomes -2\n */\nfunction fromVLQSigned(aValue) {\n  var isNegative = (aValue & 1) === 1;\n  var shifted = aValue >> 1;\n  return isNegative\n    ? -shifted\n    : shifted;\n}\n\n/**\n * Returns the base 64 VLQ encoded value.\n */\nexports.encode = function base64VLQ_encode(aValue) {\n  var encoded = \"\";\n  var digit;\n\n  var vlq = toVLQSigned(aValue);\n\n  do {\n    digit = vlq & VLQ_BASE_MASK;\n    vlq >>>= VLQ_BASE_SHIFT;\n    if (vlq > 0) {\n      // There are still more digits in this value, so we must make sure the\n      // continuation bit is marked.\n      digit |= VLQ_CONTINUATION_BIT;\n    }\n    encoded += base64.encode(digit);\n  } while (vlq > 0);\n\n  return encoded;\n};\n\n/**\n * Decodes the next base 64 VLQ value from the given string and returns the\n * value and the rest of the string via the out parameter.\n */\nexports.decode = function base64VLQ_decode(aStr, aIndex, aOutParam) {\n  var strLen = aStr.length;\n  var result = 0;\n  var shift = 0;\n  var continuation, digit;\n\n  do {\n    if (aIndex >= strLen) {\n      throw new Error(\"Expected more digits in base 64 VLQ value.\");\n    }\n\n    digit = base64.decode(aStr.charCodeAt(aIndex++));\n    if (digit === -1) {\n      throw new Error(\"Invalid base64 digit: \" + aStr.charAt(aIndex - 1));\n    }\n\n    continuation = !!(digit & VLQ_CONTINUATION_BIT);\n    digit &= VLQ_BASE_MASK;\n    result = result + (digit << shift);\n    shift += VLQ_BASE_SHIFT;\n  } while (continuation);\n\n  aOutParam.value = fromVLQSigned(result);\n  aOutParam.rest = aIndex;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/base64-vlq.js\n// module id = 2\n// module chunks = 0", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar intToCharMap = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/'.split('');\n\n/**\n * Encode an integer in the range of 0 to 63 to a single base 64 digit.\n */\nexports.encode = function (number) {\n  if (0 <= number && number < intToCharMap.length) {\n    return intToCharMap[number];\n  }\n  throw new TypeError(\"Must be between 0 and 63: \" + number);\n};\n\n/**\n * Decode a single base 64 character code digit to an integer. Returns -1 on\n * failure.\n */\nexports.decode = function (charCode) {\n  var bigA = 65;     // 'A'\n  var bigZ = 90;     // 'Z'\n\n  var littleA = 97;  // 'a'\n  var littleZ = 122; // 'z'\n\n  var zero = 48;     // '0'\n  var nine = 57;     // '9'\n\n  var plus = 43;     // '+'\n  var slash = 47;    // '/'\n\n  var littleOffset = 26;\n  var numberOffset = 52;\n\n  // 0 - 25: ABCDEFGHIJKLMNOPQRSTUVWXYZ\n  if (bigA <= charCode && charCode <= bigZ) {\n    return (charCode - bigA);\n  }\n\n  // 26 - 51: abcdefghijklmnopqrstuvwxyz\n  if (littleA <= charCode && charCode <= littleZ) {\n    return (charCode - littleA + littleOffset);\n  }\n\n  // 52 - 61: **********\n  if (zero <= charCode && charCode <= nine) {\n    return (charCode - zero + numberOffset);\n  }\n\n  // 62: +\n  if (charCode == plus) {\n    return 62;\n  }\n\n  // 63: /\n  if (charCode == slash) {\n    return 63;\n  }\n\n  // Invalid base64 digit.\n  return -1;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/base64.js\n// module id = 3\n// module chunks = 0", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n/**\n * This is a helper function for getting values from parameter/options\n * objects.\n *\n * @param args The object we are extracting values from\n * @param name The name of the property we are getting.\n * @param defaultValue An optional value to return if the property is missing\n * from the object. If this is not specified and the property is missing, an\n * error will be thrown.\n */\nfunction getArg(aArgs, aName, aDefaultValue) {\n  if (aName in aArgs) {\n    return aArgs[aName];\n  } else if (arguments.length === 3) {\n    return aDefaultValue;\n  } else {\n    throw new Error('\"' + aName + '\" is a required argument.');\n  }\n}\nexports.getArg = getArg;\n\nvar urlRegexp = /^(?:([\\w+\\-.]+):)?\\/\\/(?:(\\w+:\\w+)@)?([\\w.-]*)(?::(\\d+))?(.*)$/;\nvar dataUrlRegexp = /^data:.+\\,.+$/;\n\nfunction urlParse(aUrl) {\n  var match = aUrl.match(urlRegexp);\n  if (!match) {\n    return null;\n  }\n  return {\n    scheme: match[1],\n    auth: match[2],\n    host: match[3],\n    port: match[4],\n    path: match[5]\n  };\n}\nexports.urlParse = urlParse;\n\nfunction urlGenerate(aParsedUrl) {\n  var url = '';\n  if (aParsedUrl.scheme) {\n    url += aParsedUrl.scheme + ':';\n  }\n  url += '//';\n  if (aParsedUrl.auth) {\n    url += aParsedUrl.auth + '@';\n  }\n  if (aParsedUrl.host) {\n    url += aParsedUrl.host;\n  }\n  if (aParsedUrl.port) {\n    url += \":\" + aParsedUrl.port\n  }\n  if (aParsedUrl.path) {\n    url += aParsedUrl.path;\n  }\n  return url;\n}\nexports.urlGenerate = urlGenerate;\n\n/**\n * Normalizes a path, or the path portion of a URL:\n *\n * - Replaces consecutive slashes with one slash.\n * - Removes unnecessary '.' parts.\n * - Removes unnecessary '<dir>/..' parts.\n *\n * Based on code in the Node.js 'path' core module.\n *\n * @param aPath The path or url to normalize.\n */\nfunction normalize(aPath) {\n  var path = aPath;\n  var url = urlParse(aPath);\n  if (url) {\n    if (!url.path) {\n      return aPath;\n    }\n    path = url.path;\n  }\n  var isAbsolute = exports.isAbsolute(path);\n\n  var parts = path.split(/\\/+/);\n  for (var part, up = 0, i = parts.length - 1; i >= 0; i--) {\n    part = parts[i];\n    if (part === '.') {\n      parts.splice(i, 1);\n    } else if (part === '..') {\n      up++;\n    } else if (up > 0) {\n      if (part === '') {\n        // The first part is blank if the path is absolute. Trying to go\n        // above the root is a no-op. Therefore we can remove all '..' parts\n        // directly after the root.\n        parts.splice(i + 1, up);\n        up = 0;\n      } else {\n        parts.splice(i, 2);\n        up--;\n      }\n    }\n  }\n  path = parts.join('/');\n\n  if (path === '') {\n    path = isAbsolute ? '/' : '.';\n  }\n\n  if (url) {\n    url.path = path;\n    return urlGenerate(url);\n  }\n  return path;\n}\nexports.normalize = normalize;\n\n/**\n * Joins two paths/URLs.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be joined with the root.\n *\n * - If aPath is a URL or a data URI, aPath is returned, unless aPath is a\n *   scheme-relative URL: Then the scheme of aRoot, if any, is prepended\n *   first.\n * - Otherwise aPath is a path. If aRoot is a URL, then its path portion\n *   is updated with the result and aRoot is returned. Otherwise the result\n *   is returned.\n *   - If aPath is absolute, the result is aPath.\n *   - Otherwise the two paths are joined with a slash.\n * - Joining for example 'http://' and 'www.example.com' is also supported.\n */\nfunction join(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n  if (aPath === \"\") {\n    aPath = \".\";\n  }\n  var aPathUrl = urlParse(aPath);\n  var aRootUrl = urlParse(aRoot);\n  if (aRootUrl) {\n    aRoot = aRootUrl.path || '/';\n  }\n\n  // `join(foo, '//www.example.org')`\n  if (aPathUrl && !aPathUrl.scheme) {\n    if (aRootUrl) {\n      aPathUrl.scheme = aRootUrl.scheme;\n    }\n    return urlGenerate(aPathUrl);\n  }\n\n  if (aPathUrl || aPath.match(dataUrlRegexp)) {\n    return aPath;\n  }\n\n  // `join('http://', 'www.example.com')`\n  if (aRootUrl && !aRootUrl.host && !aRootUrl.path) {\n    aRootUrl.host = aPath;\n    return urlGenerate(aRootUrl);\n  }\n\n  var joined = aPath.charAt(0) === '/'\n    ? aPath\n    : normalize(aRoot.replace(/\\/+$/, '') + '/' + aPath);\n\n  if (aRootUrl) {\n    aRootUrl.path = joined;\n    return urlGenerate(aRootUrl);\n  }\n  return joined;\n}\nexports.join = join;\n\nexports.isAbsolute = function (aPath) {\n  return aPath.charAt(0) === '/' || urlRegexp.test(aPath);\n};\n\n/**\n * Make a path relative to a URL or another path.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be made relative to aRoot.\n */\nfunction relative(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n\n  aRoot = aRoot.replace(/\\/$/, '');\n\n  // It is possible for the path to be above the root. In this case, simply\n  // checking whether the root is a prefix of the path won't work. Instead, we\n  // need to remove components from the root one by one, until either we find\n  // a prefix that fits, or we run out of components to remove.\n  var level = 0;\n  while (aPath.indexOf(aRoot + '/') !== 0) {\n    var index = aRoot.lastIndexOf(\"/\");\n    if (index < 0) {\n      return aPath;\n    }\n\n    // If the only part of the root that is left is the scheme (i.e. http://,\n    // file:///, etc.), one or more slashes (/), or simply nothing at all, we\n    // have exhausted all components, so the path is not relative to the root.\n    aRoot = aRoot.slice(0, index);\n    if (aRoot.match(/^([^\\/]+:\\/)?\\/*$/)) {\n      return aPath;\n    }\n\n    ++level;\n  }\n\n  // Make sure we add a \"../\" for each component we removed from the root.\n  return Array(level + 1).join(\"../\") + aPath.substr(aRoot.length + 1);\n}\nexports.relative = relative;\n\nvar supportsNullProto = (function () {\n  var obj = Object.create(null);\n  return !('__proto__' in obj);\n}());\n\nfunction identity (s) {\n  return s;\n}\n\n/**\n * Because behavior goes wacky when you set `__proto__` on objects, we\n * have to prefix all the strings in our set with an arbitrary character.\n *\n * See https://github.com/mozilla/source-map/pull/31 and\n * https://github.com/mozilla/source-map/issues/30\n *\n * @param String aStr\n */\nfunction toSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return '$' + aStr;\n  }\n\n  return aStr;\n}\nexports.toSetString = supportsNullProto ? identity : toSetString;\n\nfunction fromSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return aStr.slice(1);\n  }\n\n  return aStr;\n}\nexports.fromSetString = supportsNullProto ? identity : fromSetString;\n\nfunction isProtoString(s) {\n  if (!s) {\n    return false;\n  }\n\n  var length = s.length;\n\n  if (length < 9 /* \"__proto__\".length */) {\n    return false;\n  }\n\n  if (s.charCodeAt(length - 1) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 2) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 3) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 4) !== 116 /* 't' */ ||\n      s.charCodeAt(length - 5) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 6) !== 114 /* 'r' */ ||\n      s.charCodeAt(length - 7) !== 112 /* 'p' */ ||\n      s.charCodeAt(length - 8) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 9) !== 95  /* '_' */) {\n    return false;\n  }\n\n  for (var i = length - 10; i >= 0; i--) {\n    if (s.charCodeAt(i) !== 36 /* '$' */) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Comparator between two mappings where the original positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same original source/line/column, but different generated\n * line and column the same. Useful when searching for a mapping with a\n * stubbed out mapping.\n */\nfunction compareByOriginalPositions(mappingA, mappingB, onlyCompareOriginal) {\n  var cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0 || onlyCompareOriginal) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByOriginalPositions = compareByOriginalPositions;\n\n/**\n * Comparator between two mappings with deflated source and name indices where\n * the generated positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same generated line and column, but different\n * source/name/original line and column the same. Useful when searching for a\n * mapping with a stubbed out mapping.\n */\nfunction compareByGeneratedPositionsDeflated(mappingA, mappingB, onlyCompareGenerated) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0 || onlyCompareGenerated) {\n    return cmp;\n  }\n\n  cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByGeneratedPositionsDeflated = compareByGeneratedPositionsDeflated;\n\nfunction strcmp(aStr1, aStr2) {\n  if (aStr1 === aStr2) {\n    return 0;\n  }\n\n  if (aStr1 === null) {\n    return 1; // aStr2 !== null\n  }\n\n  if (aStr2 === null) {\n    return -1; // aStr1 !== null\n  }\n\n  if (aStr1 > aStr2) {\n    return 1;\n  }\n\n  return -1;\n}\n\n/**\n * Comparator between two mappings with inflated source and name strings where\n * the generated positions are compared.\n */\nfunction compareByGeneratedPositionsInflated(mappingA, mappingB) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByGeneratedPositionsInflated = compareByGeneratedPositionsInflated;\n\n/**\n * Strip any JSON XSSI avoidance prefix from the string (as documented\n * in the source maps specification), and then parse the string as\n * JSON.\n */\nfunction parseSourceMapInput(str) {\n  return JSON.parse(str.replace(/^\\)]}'[^\\n]*\\n/, ''));\n}\nexports.parseSourceMapInput = parseSourceMapInput;\n\n/**\n * Compute the URL of a source given the the source root, the source's\n * URL, and the source map's URL.\n */\nfunction computeSourceURL(sourceRoot, sourceURL, sourceMapURL) {\n  sourceURL = sourceURL || '';\n\n  if (sourceRoot) {\n    // This follows what Chrome does.\n    if (sourceRoot[sourceRoot.length - 1] !== '/' && sourceURL[0] !== '/') {\n      sourceRoot += '/';\n    }\n    // The spec says:\n    //   Line 4: An optional source root, useful for relocating source\n    //   files on a server or removing repeated values in the\n    //   “sources” entry.  This value is prepended to the individual\n    //   entries in the “source” field.\n    sourceURL = sourceRoot + sourceURL;\n  }\n\n  // Historically, SourceMapConsumer did not take the sourceMapURL as\n  // a parameter.  This mode is still somewhat supported, which is why\n  // this code block is conditional.  However, it's preferable to pass\n  // the source map URL to SourceMapConsumer, so that this function\n  // can implement the source URL resolution algorithm as outlined in\n  // the spec.  This block is basically the equivalent of:\n  //    new URL(sourceURL, sourceMapURL).toString()\n  // ... except it avoids using URL, which wasn't available in the\n  // older releases of node still supported by this library.\n  //\n  // The spec says:\n  //   If the sources are not absolute URLs after prepending of the\n  //   “sourceRoot”, the sources are resolved relative to the\n  //   SourceMap (like resolving script src in a html document).\n  if (sourceMapURL) {\n    var parsed = urlParse(sourceMapURL);\n    if (!parsed) {\n      throw new Error(\"sourceMapURL could not be parsed\");\n    }\n    if (parsed.path) {\n      // Strip the last path component, but keep the \"/\".\n      var index = parsed.path.lastIndexOf('/');\n      if (index >= 0) {\n        parsed.path = parsed.path.substring(0, index + 1);\n      }\n    }\n    sourceURL = join(urlGenerate(parsed), sourceURL);\n  }\n\n  return normalize(sourceURL);\n}\nexports.computeSourceURL = computeSourceURL;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/util.js\n// module id = 4\n// module chunks = 0", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar has = Object.prototype.hasOwnProperty;\nvar hasNativeMap = typeof Map !== \"undefined\";\n\n/**\n * A data structure which is a combination of an array and a set. Adding a new\n * member is O(1), testing for membership is O(1), and finding the index of an\n * element is O(1). Removing elements from the set is not supported. Only\n * strings are supported for membership.\n */\nfunction ArraySet() {\n  this._array = [];\n  this._set = hasNativeMap ? new Map() : Object.create(null);\n}\n\n/**\n * Static method for creating ArraySet instances from an existing array.\n */\nArraySet.fromArray = function ArraySet_fromArray(aArray, aAllowDuplicates) {\n  var set = new ArraySet();\n  for (var i = 0, len = aArray.length; i < len; i++) {\n    set.add(aArray[i], aAllowDuplicates);\n  }\n  return set;\n};\n\n/**\n * Return how many unique items are in this ArraySet. If duplicates have been\n * added, than those do not count towards the size.\n *\n * @returns Number\n */\nArraySet.prototype.size = function ArraySet_size() {\n  return hasNativeMap ? this._set.size : Object.getOwnPropertyNames(this._set).length;\n};\n\n/**\n * Add the given string to this set.\n *\n * @param String aStr\n */\nArraySet.prototype.add = function ArraySet_add(aStr, aAllowDuplicates) {\n  var sStr = hasNativeMap ? aStr : util.toSetString(aStr);\n  var isDuplicate = hasNativeMap ? this.has(aStr) : has.call(this._set, sStr);\n  var idx = this._array.length;\n  if (!isDuplicate || aAllowDuplicates) {\n    this._array.push(aStr);\n  }\n  if (!isDuplicate) {\n    if (hasNativeMap) {\n      this._set.set(aStr, idx);\n    } else {\n      this._set[sStr] = idx;\n    }\n  }\n};\n\n/**\n * Is the given string a member of this set?\n *\n * @param String aStr\n */\nArraySet.prototype.has = function ArraySet_has(aStr) {\n  if (hasNativeMap) {\n    return this._set.has(aStr);\n  } else {\n    var sStr = util.toSetString(aStr);\n    return has.call(this._set, sStr);\n  }\n};\n\n/**\n * What is the index of the given string in the array?\n *\n * @param String aStr\n */\nArraySet.prototype.indexOf = function ArraySet_indexOf(aStr) {\n  if (hasNativeMap) {\n    var idx = this._set.get(aStr);\n    if (idx >= 0) {\n        return idx;\n    }\n  } else {\n    var sStr = util.toSetString(aStr);\n    if (has.call(this._set, sStr)) {\n      return this._set[sStr];\n    }\n  }\n\n  throw new Error('\"' + aStr + '\" is not in the set.');\n};\n\n/**\n * What is the element at the given index?\n *\n * @param Number aIdx\n */\nArraySet.prototype.at = function ArraySet_at(aIdx) {\n  if (aIdx >= 0 && aIdx < this._array.length) {\n    return this._array[aIdx];\n  }\n  throw new Error('No element indexed by ' + aIdx);\n};\n\n/**\n * Returns the array representation of this set (which has the proper indices\n * indicated by indexOf). Note that this is a copy of the internal array used\n * for storing the members so that no one can mess with internal state.\n */\nArraySet.prototype.toArray = function ArraySet_toArray() {\n  return this._array.slice();\n};\n\nexports.ArraySet = ArraySet;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/array-set.js\n// module id = 5\n// module chunks = 0", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2014 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\n\n/**\n * Determine whether mappingB is after mappingA with respect to generated\n * position.\n */\nfunction generatedPositionAfter(mappingA, mappingB) {\n  // Optimized for most common case\n  var lineA = mappingA.generatedLine;\n  var lineB = mappingB.generatedLine;\n  var columnA = mappingA.generatedColumn;\n  var columnB = mappingB.generatedColumn;\n  return lineB > lineA || lineB == lineA && columnB >= columnA ||\n         util.compareByGeneratedPositionsInflated(mappingA, mappingB) <= 0;\n}\n\n/**\n * A data structure to provide a sorted view of accumulated mappings in a\n * performance conscious manner. It trades a neglibable overhead in general\n * case for a large speedup in case of mappings being added in order.\n */\nfunction MappingList() {\n  this._array = [];\n  this._sorted = true;\n  // Serves as infimum\n  this._last = {generatedLine: -1, generatedColumn: 0};\n}\n\n/**\n * Iterate through internal items. This method takes the same arguments that\n * `Array.prototype.forEach` takes.\n *\n * NOTE: The order of the mappings is NOT guaranteed.\n */\nMappingList.prototype.unsortedForEach =\n  function MappingList_forEach(aCallback, aThisArg) {\n    this._array.forEach(aCallback, aThisArg);\n  };\n\n/**\n * Add the given source mapping.\n *\n * @param Object aMapping\n */\nMappingList.prototype.add = function MappingList_add(aMapping) {\n  if (generatedPositionAfter(this._last, aMapping)) {\n    this._last = aMapping;\n    this._array.push(aMapping);\n  } else {\n    this._sorted = false;\n    this._array.push(aMapping);\n  }\n};\n\n/**\n * Returns the flat, sorted array of mappings. The mappings are sorted by\n * generated position.\n *\n * WARNING: This method returns internal data without copying, for\n * performance. The return value must NOT be mutated, and should be treated as\n * an immutable borrow. If you want to take ownership, you must make your own\n * copy.\n */\nMappingList.prototype.toArray = function MappingList_toArray() {\n  if (!this._sorted) {\n    this._array.sort(util.compareByGeneratedPositionsInflated);\n    this._sorted = true;\n  }\n  return this._array;\n};\n\nexports.MappingList = MappingList;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/mapping-list.js\n// module id = 6\n// module chunks = 0", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar binarySearch = require('./binary-search');\nvar ArraySet = require('./array-set').ArraySet;\nvar base64VLQ = require('./base64-vlq');\nvar quickSort = require('./quick-sort').quickSort;\n\nfunction SourceMapConsumer(aSourceMap, aSourceMapURL) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = util.parseSourceMapInput(aSourceMap);\n  }\n\n  return sourceMap.sections != null\n    ? new IndexedSourceMapConsumer(sourceMap, aSourceMapURL)\n    : new BasicSourceMapConsumer(sourceMap, aSourceMapURL);\n}\n\nSourceMapConsumer.fromSourceMap = function(aSourceMap, aSourceMapURL) {\n  return BasicSourceMapConsumer.fromSourceMap(aSourceMap, aSourceMapURL);\n}\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nSourceMapConsumer.prototype._version = 3;\n\n// `__generatedMappings` and `__originalMappings` are arrays that hold the\n// parsed mapping coordinates from the source map's \"mappings\" attribute. They\n// are lazily instantiated, accessed via the `_generatedMappings` and\n// `_originalMappings` getters respectively, and we only parse the mappings\n// and create these arrays once queried for a source location. We jump through\n// these hoops because there can be many thousands of mappings, and parsing\n// them is expensive, so we only want to do it if we must.\n//\n// Each object in the arrays is of the form:\n//\n//     {\n//       generatedLine: The line number in the generated code,\n//       generatedColumn: The column number in the generated code,\n//       source: The path to the original source file that generated this\n//               chunk of code,\n//       originalLine: The line number in the original source that\n//                     corresponds to this chunk of generated code,\n//       originalColumn: The column number in the original source that\n//                       corresponds to this chunk of generated code,\n//       name: The name of the original symbol which generated this chunk of\n//             code.\n//     }\n//\n// All properties except for `generatedLine` and `generatedColumn` can be\n// `null`.\n//\n// `_generatedMappings` is ordered by the generated positions.\n//\n// `_originalMappings` is ordered by the original positions.\n\nSourceMapConsumer.prototype.__generatedMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_generatedMappings', {\n  configurable: true,\n  enumerable: true,\n  get: function () {\n    if (!this.__generatedMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__generatedMappings;\n  }\n});\n\nSourceMapConsumer.prototype.__originalMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_originalMappings', {\n  configurable: true,\n  enumerable: true,\n  get: function () {\n    if (!this.__originalMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__originalMappings;\n  }\n});\n\nSourceMapConsumer.prototype._charIsMappingSeparator =\n  function SourceMapConsumer_charIsMappingSeparator(aStr, index) {\n    var c = aStr.charAt(index);\n    return c === \";\" || c === \",\";\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    throw new Error(\"Subclasses must implement _parseMappings\");\n  };\n\nSourceMapConsumer.GENERATED_ORDER = 1;\nSourceMapConsumer.ORIGINAL_ORDER = 2;\n\nSourceMapConsumer.GREATEST_LOWER_BOUND = 1;\nSourceMapConsumer.LEAST_UPPER_BOUND = 2;\n\n/**\n * Iterate over each mapping between an original source/line/column and a\n * generated line/column in this source map.\n *\n * @param Function aCallback\n *        The function that is called with each mapping.\n * @param Object aContext\n *        Optional. If specified, this object will be the value of `this` every\n *        time that `aCallback` is called.\n * @param aOrder\n *        Either `SourceMapConsumer.GENERATED_ORDER` or\n *        `SourceMapConsumer.ORIGINAL_ORDER`. Specifies whether you want to\n *        iterate over the mappings sorted by the generated file's line/column\n *        order or the original's source/line/column order, respectively. Defaults to\n *        `SourceMapConsumer.GENERATED_ORDER`.\n */\nSourceMapConsumer.prototype.eachMapping =\n  function SourceMapConsumer_eachMapping(aCallback, aContext, aOrder) {\n    var context = aContext || null;\n    var order = aOrder || SourceMapConsumer.GENERATED_ORDER;\n\n    var mappings;\n    switch (order) {\n    case SourceMapConsumer.GENERATED_ORDER:\n      mappings = this._generatedMappings;\n      break;\n    case SourceMapConsumer.ORIGINAL_ORDER:\n      mappings = this._originalMappings;\n      break;\n    default:\n      throw new Error(\"Unknown order of iteration.\");\n    }\n\n    var sourceRoot = this.sourceRoot;\n    mappings.map(function (mapping) {\n      var source = mapping.source === null ? null : this._sources.at(mapping.source);\n      source = util.computeSourceURL(sourceRoot, source, this._sourceMapURL);\n      return {\n        source: source,\n        generatedLine: mapping.generatedLine,\n        generatedColumn: mapping.generatedColumn,\n        originalLine: mapping.originalLine,\n        originalColumn: mapping.originalColumn,\n        name: mapping.name === null ? null : this._names.at(mapping.name)\n      };\n    }, this).forEach(aCallback, context);\n  };\n\n/**\n * Returns all generated line and column information for the original source,\n * line, and column provided. If no column is provided, returns all mappings\n * corresponding to a either the line we are searching for or the next\n * closest line that has any mappings. Otherwise, returns all mappings\n * corresponding to the given line and either the column we are searching for\n * or the next closest column that has any offsets.\n *\n * The only argument is an object with the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.  The line number is 1-based.\n *   - column: Optional. the column number in the original source.\n *    The column number is 0-based.\n *\n * and an array of objects is returned, each with the following properties:\n *\n *   - line: The line number in the generated source, or null.  The\n *    line number is 1-based.\n *   - column: The column number in the generated source, or null.\n *    The column number is 0-based.\n */\nSourceMapConsumer.prototype.allGeneratedPositionsFor =\n  function SourceMapConsumer_allGeneratedPositionsFor(aArgs) {\n    var line = util.getArg(aArgs, 'line');\n\n    // When there is no exact match, BasicSourceMapConsumer.prototype._findMapping\n    // returns the index of the closest mapping less than the needle. By\n    // setting needle.originalColumn to 0, we thus find the last mapping for\n    // the given line, provided such a mapping exists.\n    var needle = {\n      source: util.getArg(aArgs, 'source'),\n      originalLine: line,\n      originalColumn: util.getArg(aArgs, 'column', 0)\n    };\n\n    needle.source = this._findSourceIndex(needle.source);\n    if (needle.source < 0) {\n      return [];\n    }\n\n    var mappings = [];\n\n    var index = this._findMapping(needle,\n                                  this._originalMappings,\n                                  \"originalLine\",\n                                  \"originalColumn\",\n                                  util.compareByOriginalPositions,\n                                  binarySearch.LEAST_UPPER_BOUND);\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (aArgs.column === undefined) {\n        var originalLine = mapping.originalLine;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we found. Since\n        // mappings are sorted, this is guaranteed to find all mappings for\n        // the line we found.\n        while (mapping && mapping.originalLine === originalLine) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      } else {\n        var originalColumn = mapping.originalColumn;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we were searching for.\n        // Since mappings are sorted, this is guaranteed to find all mappings for\n        // the line we are searching for.\n        while (mapping &&\n               mapping.originalLine === line &&\n               mapping.originalColumn == originalColumn) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      }\n    }\n\n    return mappings;\n  };\n\nexports.SourceMapConsumer = SourceMapConsumer;\n\n/**\n * A BasicSourceMapConsumer instance represents a parsed source map which we can\n * query for information about the original file positions by giving it a file\n * position in the generated source.\n *\n * The first parameter is the raw source map (either as a JSON string, or\n * already parsed to an object). According to the spec, source maps have the\n * following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - sources: An array of URLs to the original source files.\n *   - names: An array of identifiers which can be referrenced by individual mappings.\n *   - sourceRoot: Optional. The URL root from which all sources are relative.\n *   - sourcesContent: Optional. An array of contents of the original source files.\n *   - mappings: A string of base64 VLQs which contain the actual mappings.\n *   - file: Optional. The generated file this source map is associated with.\n *\n * Here is an example source map, taken from the source map spec[0]:\n *\n *     {\n *       version : 3,\n *       file: \"out.js\",\n *       sourceRoot : \"\",\n *       sources: [\"foo.js\", \"bar.js\"],\n *       names: [\"src\", \"maps\", \"are\", \"fun\"],\n *       mappings: \"AA,AB;;ABCDE;\"\n *     }\n *\n * The second parameter, if given, is a string whose value is the URL\n * at which the source map was found.  This URL is used to compute the\n * sources array.\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit?pli=1#\n */\nfunction BasicSourceMapConsumer(aSourceMap, aSourceMapURL) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = util.parseSourceMapInput(aSourceMap);\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sources = util.getArg(sourceMap, 'sources');\n  // Sass 3.3 leaves out the 'names' array, so we deviate from the spec (which\n  // requires the array) to play nice here.\n  var names = util.getArg(sourceMap, 'names', []);\n  var sourceRoot = util.getArg(sourceMap, 'sourceRoot', null);\n  var sourcesContent = util.getArg(sourceMap, 'sourcesContent', null);\n  var mappings = util.getArg(sourceMap, 'mappings');\n  var file = util.getArg(sourceMap, 'file', null);\n\n  // Once again, Sass deviates from the spec and supplies the version as a\n  // string rather than a number, so we use loose equality checking here.\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  if (sourceRoot) {\n    sourceRoot = util.normalize(sourceRoot);\n  }\n\n  sources = sources\n    .map(String)\n    // Some source maps produce relative source paths like \"./foo.js\" instead of\n    // \"foo.js\".  Normalize these first so that future comparisons will succeed.\n    // See bugzil.la/1090768.\n    .map(util.normalize)\n    // Always ensure that absolute sources are internally stored relative to\n    // the source root, if the source root is absolute. Not doing this would\n    // be particularly problematic when the source root is a prefix of the\n    // source (valid, but why??). See github issue #199 and bugzil.la/1188982.\n    .map(function (source) {\n      return sourceRoot && util.isAbsolute(sourceRoot) && util.isAbsolute(source)\n        ? util.relative(sourceRoot, source)\n        : source;\n    });\n\n  // Pass `true` below to allow duplicate names and sources. While source maps\n  // are intended to be compressed and deduplicated, the TypeScript compiler\n  // sometimes generates source maps with duplicates in them. See Github issue\n  // #72 and bugzil.la/889492.\n  this._names = ArraySet.fromArray(names.map(String), true);\n  this._sources = ArraySet.fromArray(sources, true);\n\n  this._absoluteSources = this._sources.toArray().map(function (s) {\n    return util.computeSourceURL(sourceRoot, s, aSourceMapURL);\n  });\n\n  this.sourceRoot = sourceRoot;\n  this.sourcesContent = sourcesContent;\n  this._mappings = mappings;\n  this._sourceMapURL = aSourceMapURL;\n  this.file = file;\n}\n\nBasicSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nBasicSourceMapConsumer.prototype.consumer = SourceMapConsumer;\n\n/**\n * Utility function to find the index of a source.  Returns -1 if not\n * found.\n */\nBasicSourceMapConsumer.prototype._findSourceIndex = function(aSource) {\n  var relativeSource = aSource;\n  if (this.sourceRoot != null) {\n    relativeSource = util.relative(this.sourceRoot, relativeSource);\n  }\n\n  if (this._sources.has(relativeSource)) {\n    return this._sources.indexOf(relativeSource);\n  }\n\n  // Maybe aSource is an absolute URL as returned by |sources|.  In\n  // this case we can't simply undo the transform.\n  var i;\n  for (i = 0; i < this._absoluteSources.length; ++i) {\n    if (this._absoluteSources[i] == aSource) {\n      return i;\n    }\n  }\n\n  return -1;\n};\n\n/**\n * Create a BasicSourceMapConsumer from a SourceMapGenerator.\n *\n * @param SourceMapGenerator aSourceMap\n *        The source map that will be consumed.\n * @param String aSourceMapURL\n *        The URL at which the source map can be found (optional)\n * @returns BasicSourceMapConsumer\n */\nBasicSourceMapConsumer.fromSourceMap =\n  function SourceMapConsumer_fromSourceMap(aSourceMap, aSourceMapURL) {\n    var smc = Object.create(BasicSourceMapConsumer.prototype);\n\n    var names = smc._names = ArraySet.fromArray(aSourceMap._names.toArray(), true);\n    var sources = smc._sources = ArraySet.fromArray(aSourceMap._sources.toArray(), true);\n    smc.sourceRoot = aSourceMap._sourceRoot;\n    smc.sourcesContent = aSourceMap._generateSourcesContent(smc._sources.toArray(),\n                                                            smc.sourceRoot);\n    smc.file = aSourceMap._file;\n    smc._sourceMapURL = aSourceMapURL;\n    smc._absoluteSources = smc._sources.toArray().map(function (s) {\n      return util.computeSourceURL(smc.sourceRoot, s, aSourceMapURL);\n    });\n\n    // Because we are modifying the entries (by converting string sources and\n    // names to indices into the sources and names ArraySets), we have to make\n    // a copy of the entry or else bad things happen. Shared mutable state\n    // strikes again! See github issue #191.\n\n    var generatedMappings = aSourceMap._mappings.toArray().slice();\n    var destGeneratedMappings = smc.__generatedMappings = [];\n    var destOriginalMappings = smc.__originalMappings = [];\n\n    for (var i = 0, length = generatedMappings.length; i < length; i++) {\n      var srcMapping = generatedMappings[i];\n      var destMapping = new Mapping;\n      destMapping.generatedLine = srcMapping.generatedLine;\n      destMapping.generatedColumn = srcMapping.generatedColumn;\n\n      if (srcMapping.source) {\n        destMapping.source = sources.indexOf(srcMapping.source);\n        destMapping.originalLine = srcMapping.originalLine;\n        destMapping.originalColumn = srcMapping.originalColumn;\n\n        if (srcMapping.name) {\n          destMapping.name = names.indexOf(srcMapping.name);\n        }\n\n        destOriginalMappings.push(destMapping);\n      }\n\n      destGeneratedMappings.push(destMapping);\n    }\n\n    quickSort(smc.__originalMappings, util.compareByOriginalPositions);\n\n    return smc;\n  };\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nBasicSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(BasicSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    return this._absoluteSources.slice();\n  }\n});\n\n/**\n * Provide the JIT with a nice shape / hidden class.\n */\nfunction Mapping() {\n  this.generatedLine = 0;\n  this.generatedColumn = 0;\n  this.source = null;\n  this.originalLine = null;\n  this.originalColumn = null;\n  this.name = null;\n}\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nBasicSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    var generatedLine = 1;\n    var previousGeneratedColumn = 0;\n    var previousOriginalLine = 0;\n    var previousOriginalColumn = 0;\n    var previousSource = 0;\n    var previousName = 0;\n    var length = aStr.length;\n    var index = 0;\n    var cachedSegments = {};\n    var temp = {};\n    var originalMappings = [];\n    var generatedMappings = [];\n    var mapping, str, segment, end, value;\n\n    while (index < length) {\n      if (aStr.charAt(index) === ';') {\n        generatedLine++;\n        index++;\n        previousGeneratedColumn = 0;\n      }\n      else if (aStr.charAt(index) === ',') {\n        index++;\n      }\n      else {\n        mapping = new Mapping();\n        mapping.generatedLine = generatedLine;\n\n        // Because each offset is encoded relative to the previous one,\n        // many segments often have the same encoding. We can exploit this\n        // fact by caching the parsed variable length fields of each segment,\n        // allowing us to avoid a second parse if we encounter the same\n        // segment again.\n        for (end = index; end < length; end++) {\n          if (this._charIsMappingSeparator(aStr, end)) {\n            break;\n          }\n        }\n        str = aStr.slice(index, end);\n\n        segment = cachedSegments[str];\n        if (segment) {\n          index += str.length;\n        } else {\n          segment = [];\n          while (index < end) {\n            base64VLQ.decode(aStr, index, temp);\n            value = temp.value;\n            index = temp.rest;\n            segment.push(value);\n          }\n\n          if (segment.length === 2) {\n            throw new Error('Found a source, but no line and column');\n          }\n\n          if (segment.length === 3) {\n            throw new Error('Found a source and line, but no column');\n          }\n\n          cachedSegments[str] = segment;\n        }\n\n        // Generated column.\n        mapping.generatedColumn = previousGeneratedColumn + segment[0];\n        previousGeneratedColumn = mapping.generatedColumn;\n\n        if (segment.length > 1) {\n          // Original source.\n          mapping.source = previousSource + segment[1];\n          previousSource += segment[1];\n\n          // Original line.\n          mapping.originalLine = previousOriginalLine + segment[2];\n          previousOriginalLine = mapping.originalLine;\n          // Lines are stored 0-based\n          mapping.originalLine += 1;\n\n          // Original column.\n          mapping.originalColumn = previousOriginalColumn + segment[3];\n          previousOriginalColumn = mapping.originalColumn;\n\n          if (segment.length > 4) {\n            // Original name.\n            mapping.name = previousName + segment[4];\n            previousName += segment[4];\n          }\n        }\n\n        generatedMappings.push(mapping);\n        if (typeof mapping.originalLine === 'number') {\n          originalMappings.push(mapping);\n        }\n      }\n    }\n\n    quickSort(generatedMappings, util.compareByGeneratedPositionsDeflated);\n    this.__generatedMappings = generatedMappings;\n\n    quickSort(originalMappings, util.compareByOriginalPositions);\n    this.__originalMappings = originalMappings;\n  };\n\n/**\n * Find the mapping that best matches the hypothetical \"needle\" mapping that\n * we are searching for in the given \"haystack\" of mappings.\n */\nBasicSourceMapConsumer.prototype._findMapping =\n  function SourceMapConsumer_findMapping(aNeedle, aMappings, aLineName,\n                                         aColumnName, aComparator, aBias) {\n    // To return the position we are searching for, we must first find the\n    // mapping for the given position and then return the opposite position it\n    // points to. Because the mappings are sorted, we can use binary search to\n    // find the best mapping.\n\n    if (aNeedle[aLineName] <= 0) {\n      throw new TypeError('Line must be greater than or equal to 1, got '\n                          + aNeedle[aLineName]);\n    }\n    if (aNeedle[aColumnName] < 0) {\n      throw new TypeError('Column must be greater than or equal to 0, got '\n                          + aNeedle[aColumnName]);\n    }\n\n    return binarySearch.search(aNeedle, aMappings, aComparator, aBias);\n  };\n\n/**\n * Compute the last column for each generated mapping. The last column is\n * inclusive.\n */\nBasicSourceMapConsumer.prototype.computeColumnSpans =\n  function SourceMapConsumer_computeColumnSpans() {\n    for (var index = 0; index < this._generatedMappings.length; ++index) {\n      var mapping = this._generatedMappings[index];\n\n      // Mappings do not contain a field for the last generated columnt. We\n      // can come up with an optimistic estimate, however, by assuming that\n      // mappings are contiguous (i.e. given two consecutive mappings, the\n      // first mapping ends where the second one starts).\n      if (index + 1 < this._generatedMappings.length) {\n        var nextMapping = this._generatedMappings[index + 1];\n\n        if (mapping.generatedLine === nextMapping.generatedLine) {\n          mapping.lastGeneratedColumn = nextMapping.generatedColumn - 1;\n          continue;\n        }\n      }\n\n      // The last mapping for each line spans the entire line.\n      mapping.lastGeneratedColumn = Infinity;\n    }\n  };\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.  The line number\n *     is 1-based.\n *   - column: The column number in the generated source.  The column\n *     number is 0-based.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.  The\n *     line number is 1-based.\n *   - column: The column number in the original source, or null.  The\n *     column number is 0-based.\n *   - name: The original identifier, or null.\n */\nBasicSourceMapConsumer.prototype.originalPositionFor =\n  function SourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._generatedMappings,\n      \"generatedLine\",\n      \"generatedColumn\",\n      util.compareByGeneratedPositionsDeflated,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._generatedMappings[index];\n\n      if (mapping.generatedLine === needle.generatedLine) {\n        var source = util.getArg(mapping, 'source', null);\n        if (source !== null) {\n          source = this._sources.at(source);\n          source = util.computeSourceURL(this.sourceRoot, source, this._sourceMapURL);\n        }\n        var name = util.getArg(mapping, 'name', null);\n        if (name !== null) {\n          name = this._names.at(name);\n        }\n        return {\n          source: source,\n          line: util.getArg(mapping, 'originalLine', null),\n          column: util.getArg(mapping, 'originalColumn', null),\n          name: name\n        };\n      }\n    }\n\n    return {\n      source: null,\n      line: null,\n      column: null,\n      name: null\n    };\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nBasicSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function BasicSourceMapConsumer_hasContentsOfAllSources() {\n    if (!this.sourcesContent) {\n      return false;\n    }\n    return this.sourcesContent.length >= this._sources.size() &&\n      !this.sourcesContent.some(function (sc) { return sc == null; });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nBasicSourceMapConsumer.prototype.sourceContentFor =\n  function SourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    if (!this.sourcesContent) {\n      return null;\n    }\n\n    var index = this._findSourceIndex(aSource);\n    if (index >= 0) {\n      return this.sourcesContent[index];\n    }\n\n    var relativeSource = aSource;\n    if (this.sourceRoot != null) {\n      relativeSource = util.relative(this.sourceRoot, relativeSource);\n    }\n\n    var url;\n    if (this.sourceRoot != null\n        && (url = util.urlParse(this.sourceRoot))) {\n      // XXX: file:// URIs and absolute paths lead to unexpected behavior for\n      // many users. We can help them out when they expect file:// URIs to\n      // behave like it would if they were running a local HTTP server. See\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=885597.\n      var fileUriAbsPath = relativeSource.replace(/^file:\\/\\//, \"\");\n      if (url.scheme == \"file\"\n          && this._sources.has(fileUriAbsPath)) {\n        return this.sourcesContent[this._sources.indexOf(fileUriAbsPath)]\n      }\n\n      if ((!url.path || url.path == \"/\")\n          && this._sources.has(\"/\" + relativeSource)) {\n        return this.sourcesContent[this._sources.indexOf(\"/\" + relativeSource)];\n      }\n    }\n\n    // This function is used recursively from\n    // IndexedSourceMapConsumer.prototype.sourceContentFor. In that case, we\n    // don't want to throw if we can't find the source - we just want to\n    // return null, so we provide a flag to exit gracefully.\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + relativeSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.  The line number\n *     is 1-based.\n *   - column: The column number in the original source.  The column\n *     number is 0-based.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.  The\n *     line number is 1-based.\n *   - column: The column number in the generated source, or null.\n *     The column number is 0-based.\n */\nBasicSourceMapConsumer.prototype.generatedPositionFor =\n  function SourceMapConsumer_generatedPositionFor(aArgs) {\n    var source = util.getArg(aArgs, 'source');\n    source = this._findSourceIndex(source);\n    if (source < 0) {\n      return {\n        line: null,\n        column: null,\n        lastColumn: null\n      };\n    }\n\n    var needle = {\n      source: source,\n      originalLine: util.getArg(aArgs, 'line'),\n      originalColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._originalMappings,\n      \"originalLine\",\n      \"originalColumn\",\n      util.compareByOriginalPositions,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (mapping.source === needle.source) {\n        return {\n          line: util.getArg(mapping, 'generatedLine', null),\n          column: util.getArg(mapping, 'generatedColumn', null),\n          lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n        };\n      }\n    }\n\n    return {\n      line: null,\n      column: null,\n      lastColumn: null\n    };\n  };\n\nexports.BasicSourceMapConsumer = BasicSourceMapConsumer;\n\n/**\n * An IndexedSourceMapConsumer instance represents a parsed source map which\n * we can query for information. It differs from BasicSourceMapConsumer in\n * that it takes \"indexed\" source maps (i.e. ones with a \"sections\" field) as\n * input.\n *\n * The first parameter is a raw source map (either as a JSON string, or already\n * parsed to an object). According to the spec for indexed source maps, they\n * have the following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - file: Optional. The generated file this source map is associated with.\n *   - sections: A list of section definitions.\n *\n * Each value under the \"sections\" field has two fields:\n *   - offset: The offset into the original specified at which this section\n *       begins to apply, defined as an object with a \"line\" and \"column\"\n *       field.\n *   - map: A source map definition. This source map could also be indexed,\n *       but doesn't have to be.\n *\n * Instead of the \"map\" field, it's also possible to have a \"url\" field\n * specifying a URL to retrieve a source map from, but that's currently\n * unsupported.\n *\n * Here's an example source map, taken from the source map spec[0], but\n * modified to omit a section which uses the \"url\" field.\n *\n *  {\n *    version : 3,\n *    file: \"app.js\",\n *    sections: [{\n *      offset: {line:100, column:10},\n *      map: {\n *        version : 3,\n *        file: \"section.js\",\n *        sources: [\"foo.js\", \"bar.js\"],\n *        names: [\"src\", \"maps\", \"are\", \"fun\"],\n *        mappings: \"AAAA,E;;ABCDE;\"\n *      }\n *    }],\n *  }\n *\n * The second parameter, if given, is a string whose value is the URL\n * at which the source map was found.  This URL is used to compute the\n * sources array.\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit#heading=h.535es3xeprgt\n */\nfunction IndexedSourceMapConsumer(aSourceMap, aSourceMapURL) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = util.parseSourceMapInput(aSourceMap);\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sections = util.getArg(sourceMap, 'sections');\n\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  this._sources = new ArraySet();\n  this._names = new ArraySet();\n\n  var lastOffset = {\n    line: -1,\n    column: 0\n  };\n  this._sections = sections.map(function (s) {\n    if (s.url) {\n      // The url field will require support for asynchronicity.\n      // See https://github.com/mozilla/source-map/issues/16\n      throw new Error('Support for url field in sections not implemented.');\n    }\n    var offset = util.getArg(s, 'offset');\n    var offsetLine = util.getArg(offset, 'line');\n    var offsetColumn = util.getArg(offset, 'column');\n\n    if (offsetLine < lastOffset.line ||\n        (offsetLine === lastOffset.line && offsetColumn < lastOffset.column)) {\n      throw new Error('Section offsets must be ordered and non-overlapping.');\n    }\n    lastOffset = offset;\n\n    return {\n      generatedOffset: {\n        // The offset fields are 0-based, but we use 1-based indices when\n        // encoding/decoding from VLQ.\n        generatedLine: offsetLine + 1,\n        generatedColumn: offsetColumn + 1\n      },\n      consumer: new SourceMapConsumer(util.getArg(s, 'map'), aSourceMapURL)\n    }\n  });\n}\n\nIndexedSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nIndexedSourceMapConsumer.prototype.constructor = SourceMapConsumer;\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nIndexedSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(IndexedSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    var sources = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      for (var j = 0; j < this._sections[i].consumer.sources.length; j++) {\n        sources.push(this._sections[i].consumer.sources[j]);\n      }\n    }\n    return sources;\n  }\n});\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.  The line number\n *     is 1-based.\n *   - column: The column number in the generated source.  The column\n *     number is 0-based.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.  The\n *     line number is 1-based.\n *   - column: The column number in the original source, or null.  The\n *     column number is 0-based.\n *   - name: The original identifier, or null.\n */\nIndexedSourceMapConsumer.prototype.originalPositionFor =\n  function IndexedSourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    // Find the section containing the generated position we're trying to map\n    // to an original position.\n    var sectionIndex = binarySearch.search(needle, this._sections,\n      function(needle, section) {\n        var cmp = needle.generatedLine - section.generatedOffset.generatedLine;\n        if (cmp) {\n          return cmp;\n        }\n\n        return (needle.generatedColumn -\n                section.generatedOffset.generatedColumn);\n      });\n    var section = this._sections[sectionIndex];\n\n    if (!section) {\n      return {\n        source: null,\n        line: null,\n        column: null,\n        name: null\n      };\n    }\n\n    return section.consumer.originalPositionFor({\n      line: needle.generatedLine -\n        (section.generatedOffset.generatedLine - 1),\n      column: needle.generatedColumn -\n        (section.generatedOffset.generatedLine === needle.generatedLine\n         ? section.generatedOffset.generatedColumn - 1\n         : 0),\n      bias: aArgs.bias\n    });\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nIndexedSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function IndexedSourceMapConsumer_hasContentsOfAllSources() {\n    return this._sections.every(function (s) {\n      return s.consumer.hasContentsOfAllSources();\n    });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nIndexedSourceMapConsumer.prototype.sourceContentFor =\n  function IndexedSourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      var content = section.consumer.sourceContentFor(aSource, true);\n      if (content) {\n        return content;\n      }\n    }\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + aSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.  The line number\n *     is 1-based.\n *   - column: The column number in the original source.  The column\n *     number is 0-based.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.  The\n *     line number is 1-based. \n *   - column: The column number in the generated source, or null.\n *     The column number is 0-based.\n */\nIndexedSourceMapConsumer.prototype.generatedPositionFor =\n  function IndexedSourceMapConsumer_generatedPositionFor(aArgs) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      // Only consider this section if the requested source is in the list of\n      // sources of the consumer.\n      if (section.consumer._findSourceIndex(util.getArg(aArgs, 'source')) === -1) {\n        continue;\n      }\n      var generatedPosition = section.consumer.generatedPositionFor(aArgs);\n      if (generatedPosition) {\n        var ret = {\n          line: generatedPosition.line +\n            (section.generatedOffset.generatedLine - 1),\n          column: generatedPosition.column +\n            (section.generatedOffset.generatedLine === generatedPosition.line\n             ? section.generatedOffset.generatedColumn - 1\n             : 0)\n        };\n        return ret;\n      }\n    }\n\n    return {\n      line: null,\n      column: null\n    };\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nIndexedSourceMapConsumer.prototype._parseMappings =\n  function IndexedSourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    this.__generatedMappings = [];\n    this.__originalMappings = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n      var sectionMappings = section.consumer._generatedMappings;\n      for (var j = 0; j < sectionMappings.length; j++) {\n        var mapping = sectionMappings[j];\n\n        var source = section.consumer._sources.at(mapping.source);\n        source = util.computeSourceURL(section.consumer.sourceRoot, source, this._sourceMapURL);\n        this._sources.add(source);\n        source = this._sources.indexOf(source);\n\n        var name = null;\n        if (mapping.name) {\n          name = section.consumer._names.at(mapping.name);\n          this._names.add(name);\n          name = this._names.indexOf(name);\n        }\n\n        // The mappings coming from the consumer for the section have\n        // generated positions relative to the start of the section, so we\n        // need to offset them to be relative to the start of the concatenated\n        // generated file.\n        var adjustedMapping = {\n          source: source,\n          generatedLine: mapping.generatedLine +\n            (section.generatedOffset.generatedLine - 1),\n          generatedColumn: mapping.generatedColumn +\n            (section.generatedOffset.generatedLine === mapping.generatedLine\n            ? section.generatedOffset.generatedColumn - 1\n            : 0),\n          originalLine: mapping.originalLine,\n          originalColumn: mapping.originalColumn,\n          name: name\n        };\n\n        this.__generatedMappings.push(adjustedMapping);\n        if (typeof adjustedMapping.originalLine === 'number') {\n          this.__originalMappings.push(adjustedMapping);\n        }\n      }\n    }\n\n    quickSort(this.__generatedMappings, util.compareByGeneratedPositionsDeflated);\n    quickSort(this.__originalMappings, util.compareByOriginalPositions);\n  };\n\nexports.IndexedSourceMapConsumer = IndexedSourceMapConsumer;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/source-map-consumer.js\n// module id = 7\n// module chunks = 0", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nexports.GREATEST_LOWER_BOUND = 1;\nexports.LEAST_UPPER_BOUND = 2;\n\n/**\n * Recursive implementation of binary search.\n *\n * @param aLow Indices here and lower do not contain the needle.\n * @param aHigh Indices here and higher do not contain the needle.\n * @param aNeedle The element being searched for.\n * @param aHaystack The non-empty array being searched.\n * @param aCompare Function which takes two elements and returns -1, 0, or 1.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n */\nfunction recursiveSearch(aLow, aHigh, a<PERSON>eed<PERSON>, aHaystack, aCompare, a<PERSON><PERSON>) {\n  // This function terminates when one of the following is true:\n  //\n  //   1. We find the exact element we are looking for.\n  //\n  //   2. We did not find the exact element, but we can return the index of\n  //      the next-closest element.\n  //\n  //   3. We did not find the exact element, and there is no next-closest\n  //      element than the one we are searching for, so we return -1.\n  var mid = Math.floor((aHigh - aLow) / 2) + aLow;\n  var cmp = aCompare(aNeedle, aHaystack[mid], true);\n  if (cmp === 0) {\n    // Found the element we are looking for.\n    return mid;\n  }\n  else if (cmp > 0) {\n    // Our needle is greater than aHaystack[mid].\n    if (aHigh - mid > 1) {\n      // The element is in the upper half.\n      return recursiveSearch(mid, aHigh, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // The exact needle element was not found in this haystack. Determine if\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return aHigh < aHaystack.length ? aHigh : -1;\n    } else {\n      return mid;\n    }\n  }\n  else {\n    // Our needle is less than aHaystack[mid].\n    if (mid - aLow > 1) {\n      // The element is in the lower half.\n      return recursiveSearch(aLow, mid, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return mid;\n    } else {\n      return aLow < 0 ? -1 : aLow;\n    }\n  }\n}\n\n/**\n * This is an implementation of binary search which will always try and return\n * the index of the closest element if there is no exact hit. This is because\n * mappings between original and generated line/col pairs are single points,\n * and there is an implicit region between each of them, so a miss just means\n * that you aren't on the very start of a region.\n *\n * @param aNeedle The element you are looking for.\n * @param aHaystack The array that is being searched.\n * @param aCompare A function which takes the needle and an element in the\n *     array and returns -1, 0, or 1 depending on whether the needle is less\n *     than, equal to, or greater than the element, respectively.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'binarySearch.GREATEST_LOWER_BOUND'.\n */\nexports.search = function search(aNeedle, aHaystack, aCompare, aBias) {\n  if (aHaystack.length === 0) {\n    return -1;\n  }\n\n  var index = recursiveSearch(-1, aHaystack.length, aNeedle, aHaystack,\n                              aCompare, aBias || exports.GREATEST_LOWER_BOUND);\n  if (index < 0) {\n    return -1;\n  }\n\n  // We have found either the exact element, or the next-closest element than\n  // the one we are searching for. However, there may be more than one such\n  // element. Make sure we always return the smallest of these.\n  while (index - 1 >= 0) {\n    if (aCompare(aHaystack[index], aHaystack[index - 1], true) !== 0) {\n      break;\n    }\n    --index;\n  }\n\n  return index;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/binary-search.js\n// module id = 8\n// module chunks = 0", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n// It turns out that some (most?) JavaScript engines don't self-host\n// `Array.prototype.sort`. This makes sense because C++ will likely remain\n// faster than JS when doing raw CPU-intensive sorting. However, when using a\n// custom comparator function, calling back and forth between the VM's C++ and\n// JIT'd JS is rather slow *and* loses JIT type information, resulting in\n// worse generated code for the comparator function than would be optimal. In\n// fact, when sorting with a comparator, these costs outweigh the benefits of\n// sorting in C++. By using our own JS-implemented Quick Sort (below), we get\n// a ~3500ms mean speed-up in `bench/bench.html`.\n\n/**\n * Swap the elements indexed by `x` and `y` in the array `ary`.\n *\n * @param {Array} ary\n *        The array.\n * @param {Number} x\n *        The index of the first item.\n * @param {Number} y\n *        The index of the second item.\n */\nfunction swap(ary, x, y) {\n  var temp = ary[x];\n  ary[x] = ary[y];\n  ary[y] = temp;\n}\n\n/**\n * Returns a random integer within the range `low .. high` inclusive.\n *\n * @param {Number} low\n *        The lower bound on the range.\n * @param {Number} high\n *        The upper bound on the range.\n */\nfunction randomIntInRange(low, high) {\n  return Math.round(low + (Math.random() * (high - low)));\n}\n\n/**\n * The Quick Sort algorithm.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n * @param {Number} p\n *        Start index of the array\n * @param {Number} r\n *        End index of the array\n */\nfunction doQuickSort(ary, comparator, p, r) {\n  // If our lower bound is less than our upper bound, we (1) partition the\n  // array into two pieces and (2) recurse on each half. If it is not, this is\n  // the empty array and our base case.\n\n  if (p < r) {\n    // (1) Partitioning.\n    //\n    // The partitioning chooses a pivot between `p` and `r` and moves all\n    // elements that are less than or equal to the pivot to the before it, and\n    // all the elements that are greater than it after it. The effect is that\n    // once partition is done, the pivot is in the exact place it will be when\n    // the array is put in sorted order, and it will not need to be moved\n    // again. This runs in O(n) time.\n\n    // Always choose a random pivot so that an input array which is reverse\n    // sorted does not cause O(n^2) running time.\n    var pivotIndex = randomIntInRange(p, r);\n    var i = p - 1;\n\n    swap(ary, pivotIndex, r);\n    var pivot = ary[r];\n\n    // Immediately after `j` is incremented in this loop, the following hold\n    // true:\n    //\n    //   * Every element in `ary[p .. i]` is less than or equal to the pivot.\n    //\n    //   * Every element in `ary[i+1 .. j-1]` is greater than the pivot.\n    for (var j = p; j < r; j++) {\n      if (comparator(ary[j], pivot) <= 0) {\n        i += 1;\n        swap(ary, i, j);\n      }\n    }\n\n    swap(ary, i + 1, j);\n    var q = i + 1;\n\n    // (2) Recurse on each half.\n\n    doQuickSort(ary, comparator, p, q - 1);\n    doQuickSort(ary, comparator, q + 1, r);\n  }\n}\n\n/**\n * Sort the given array in-place with the given comparator function.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n */\nexports.quickSort = function (ary, comparator) {\n  doQuickSort(ary, comparator, 0, ary.length - 1);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/quick-sort.js\n// module id = 9\n// module chunks = 0", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar SourceMapGenerator = require('./source-map-generator').SourceMapGenerator;\nvar util = require('./util');\n\n// Matches a Windows-style `\\r\\n` newline or a `\\n` newline used by all other\n// operating systems these days (capturing the result).\nvar REGEX_NEWLINE = /(\\r?\\n)/;\n\n// Newline character code for charCodeAt() comparisons\nvar NEWLINE_CODE = 10;\n\n// Private symbol for identifying `SourceNode`s when multiple versions of\n// the source-map library are loaded. This MUST NOT CHANGE across\n// versions!\nvar isSourceNode = \"$$$isSourceNode$$$\";\n\n/**\n * SourceNodes provide a way to abstract over interpolating/concatenating\n * snippets of generated JavaScript source code while maintaining the line and\n * column information associated with the original source code.\n *\n * @param aLine The original line number.\n * @param aColumn The original column number.\n * @param aSource The original source's filename.\n * @param aChunks Optional. An array of strings which are snippets of\n *        generated JS, or other SourceNodes.\n * @param aName The original identifier.\n */\nfunction SourceNode(aLine, aColumn, aSource, aChunks, aName) {\n  this.children = [];\n  this.sourceContents = {};\n  this.line = aLine == null ? null : aLine;\n  this.column = aColumn == null ? null : aColumn;\n  this.source = aSource == null ? null : aSource;\n  this.name = aName == null ? null : aName;\n  this[isSourceNode] = true;\n  if (aChunks != null) this.add(aChunks);\n}\n\n/**\n * Creates a SourceNode from generated code and a SourceMapConsumer.\n *\n * @param aGeneratedCode The generated code\n * @param aSourceMapConsumer The SourceMap for the generated code\n * @param aRelativePath Optional. The path that relative sources in the\n *        SourceMapConsumer should be relative to.\n */\nSourceNode.fromStringWithSourceMap =\n  function SourceNode_fromStringWithSourceMap(aGeneratedCode, aSourceMapConsumer, aRelativePath) {\n    // The SourceNode we want to fill with the generated code\n    // and the SourceMap\n    var node = new SourceNode();\n\n    // All even indices of this array are one line of the generated code,\n    // while all odd indices are the newlines between two adjacent lines\n    // (since `REGEX_NEWLINE` captures its match).\n    // Processed fragments are accessed by calling `shiftNextLine`.\n    var remainingLines = aGeneratedCode.split(REGEX_NEWLINE);\n    var remainingLinesIndex = 0;\n    var shiftNextLine = function() {\n      var lineContents = getNextLine();\n      // The last line of a file might not have a newline.\n      var newLine = getNextLine() || \"\";\n      return lineContents + newLine;\n\n      function getNextLine() {\n        return remainingLinesIndex < remainingLines.length ?\n            remainingLines[remainingLinesIndex++] : undefined;\n      }\n    };\n\n    // We need to remember the position of \"remainingLines\"\n    var lastGeneratedLine = 1, lastGeneratedColumn = 0;\n\n    // The generate SourceNodes we need a code range.\n    // To extract it current and last mapping is used.\n    // Here we store the last mapping.\n    var lastMapping = null;\n\n    aSourceMapConsumer.eachMapping(function (mapping) {\n      if (lastMapping !== null) {\n        // We add the code from \"lastMapping\" to \"mapping\":\n        // First check if there is a new line in between.\n        if (lastGeneratedLine < mapping.generatedLine) {\n          // Associate first line with \"lastMapping\"\n          addMappingWithCode(lastMapping, shiftNextLine());\n          lastGeneratedLine++;\n          lastGeneratedColumn = 0;\n          // The remaining code is added without mapping\n        } else {\n          // There is no new line in between.\n          // Associate the code between \"lastGeneratedColumn\" and\n          // \"mapping.generatedColumn\" with \"lastMapping\"\n          var nextLine = remainingLines[remainingLinesIndex] || '';\n          var code = nextLine.substr(0, mapping.generatedColumn -\n                                        lastGeneratedColumn);\n          remainingLines[remainingLinesIndex] = nextLine.substr(mapping.generatedColumn -\n                                              lastGeneratedColumn);\n          lastGeneratedColumn = mapping.generatedColumn;\n          addMappingWithCode(lastMapping, code);\n          // No more remaining code, continue\n          lastMapping = mapping;\n          return;\n        }\n      }\n      // We add the generated code until the first mapping\n      // to the SourceNode without any mapping.\n      // Each line is added as separate string.\n      while (lastGeneratedLine < mapping.generatedLine) {\n        node.add(shiftNextLine());\n        lastGeneratedLine++;\n      }\n      if (lastGeneratedColumn < mapping.generatedColumn) {\n        var nextLine = remainingLines[remainingLinesIndex] || '';\n        node.add(nextLine.substr(0, mapping.generatedColumn));\n        remainingLines[remainingLinesIndex] = nextLine.substr(mapping.generatedColumn);\n        lastGeneratedColumn = mapping.generatedColumn;\n      }\n      lastMapping = mapping;\n    }, this);\n    // We have processed all mappings.\n    if (remainingLinesIndex < remainingLines.length) {\n      if (lastMapping) {\n        // Associate the remaining code in the current line with \"lastMapping\"\n        addMappingWithCode(lastMapping, shiftNextLine());\n      }\n      // and add the remaining lines without any mapping\n      node.add(remainingLines.splice(remainingLinesIndex).join(\"\"));\n    }\n\n    // Copy sourcesContent into SourceNode\n    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n      if (content != null) {\n        if (aRelativePath != null) {\n          sourceFile = util.join(aRelativePath, sourceFile);\n        }\n        node.setSourceContent(sourceFile, content);\n      }\n    });\n\n    return node;\n\n    function addMappingWithCode(mapping, code) {\n      if (mapping === null || mapping.source === undefined) {\n        node.add(code);\n      } else {\n        var source = aRelativePath\n          ? util.join(aRelativePath, mapping.source)\n          : mapping.source;\n        node.add(new SourceNode(mapping.originalLine,\n                                mapping.originalColumn,\n                                source,\n                                code,\n                                mapping.name));\n      }\n    }\n  };\n\n/**\n * Add a chunk of generated JS to this source node.\n *\n * @param aChunk A string snippet of generated JS code, another instance of\n *        SourceNode, or an array where each member is one of those things.\n */\nSourceNode.prototype.add = function SourceNode_add(aChunk) {\n  if (Array.isArray(aChunk)) {\n    aChunk.forEach(function (chunk) {\n      this.add(chunk);\n    }, this);\n  }\n  else if (aChunk[isSourceNode] || typeof aChunk === \"string\") {\n    if (aChunk) {\n      this.children.push(aChunk);\n    }\n  }\n  else {\n    throw new TypeError(\n      \"Expected a SourceNode, string, or an array of SourceNodes and strings. Got \" + aChunk\n    );\n  }\n  return this;\n};\n\n/**\n * Add a chunk of generated JS to the beginning of this source node.\n *\n * @param aChunk A string snippet of generated JS code, another instance of\n *        SourceNode, or an array where each member is one of those things.\n */\nSourceNode.prototype.prepend = function SourceNode_prepend(aChunk) {\n  if (Array.isArray(aChunk)) {\n    for (var i = aChunk.length-1; i >= 0; i--) {\n      this.prepend(aChunk[i]);\n    }\n  }\n  else if (aChunk[isSourceNode] || typeof aChunk === \"string\") {\n    this.children.unshift(aChunk);\n  }\n  else {\n    throw new TypeError(\n      \"Expected a SourceNode, string, or an array of SourceNodes and strings. Got \" + aChunk\n    );\n  }\n  return this;\n};\n\n/**\n * Walk over the tree of JS snippets in this node and its children. The\n * walking function is called once for each snippet of JS and is passed that\n * snippet and the its original associated source's line/column location.\n *\n * @param aFn The traversal function.\n */\nSourceNode.prototype.walk = function SourceNode_walk(aFn) {\n  var chunk;\n  for (var i = 0, len = this.children.length; i < len; i++) {\n    chunk = this.children[i];\n    if (chunk[isSourceNode]) {\n      chunk.walk(aFn);\n    }\n    else {\n      if (chunk !== '') {\n        aFn(chunk, { source: this.source,\n                     line: this.line,\n                     column: this.column,\n                     name: this.name });\n      }\n    }\n  }\n};\n\n/**\n * Like `String.prototype.join` except for SourceNodes. Inserts `aStr` between\n * each of `this.children`.\n *\n * @param aSep The separator.\n */\nSourceNode.prototype.join = function SourceNode_join(aSep) {\n  var newChildren;\n  var i;\n  var len = this.children.length;\n  if (len > 0) {\n    newChildren = [];\n    for (i = 0; i < len-1; i++) {\n      newChildren.push(this.children[i]);\n      newChildren.push(aSep);\n    }\n    newChildren.push(this.children[i]);\n    this.children = newChildren;\n  }\n  return this;\n};\n\n/**\n * Call String.prototype.replace on the very right-most source snippet. Useful\n * for trimming whitespace from the end of a source node, etc.\n *\n * @param aPattern The pattern to replace.\n * @param aReplacement The thing to replace the pattern with.\n */\nSourceNode.prototype.replaceRight = function SourceNode_replaceRight(aPattern, aReplacement) {\n  var lastChild = this.children[this.children.length - 1];\n  if (lastChild[isSourceNode]) {\n    lastChild.replaceRight(aPattern, aReplacement);\n  }\n  else if (typeof lastChild === 'string') {\n    this.children[this.children.length - 1] = lastChild.replace(aPattern, aReplacement);\n  }\n  else {\n    this.children.push(''.replace(aPattern, aReplacement));\n  }\n  return this;\n};\n\n/**\n * Set the source content for a source file. This will be added to the SourceMapGenerator\n * in the sourcesContent field.\n *\n * @param aSourceFile The filename of the source file\n * @param aSourceContent The content of the source file\n */\nSourceNode.prototype.setSourceContent =\n  function SourceNode_setSourceContent(aSourceFile, aSourceContent) {\n    this.sourceContents[util.toSetString(aSourceFile)] = aSourceContent;\n  };\n\n/**\n * Walk over the tree of SourceNodes. The walking function is called for each\n * source file content and is passed the filename and source content.\n *\n * @param aFn The traversal function.\n */\nSourceNode.prototype.walkSourceContents =\n  function SourceNode_walkSourceContents(aFn) {\n    for (var i = 0, len = this.children.length; i < len; i++) {\n      if (this.children[i][isSourceNode]) {\n        this.children[i].walkSourceContents(aFn);\n      }\n    }\n\n    var sources = Object.keys(this.sourceContents);\n    for (var i = 0, len = sources.length; i < len; i++) {\n      aFn(util.fromSetString(sources[i]), this.sourceContents[sources[i]]);\n    }\n  };\n\n/**\n * Return the string representation of this source node. Walks over the tree\n * and concatenates all the various snippets together to one string.\n */\nSourceNode.prototype.toString = function SourceNode_toString() {\n  var str = \"\";\n  this.walk(function (chunk) {\n    str += chunk;\n  });\n  return str;\n};\n\n/**\n * Returns the string representation of this source node along with a source\n * map.\n */\nSourceNode.prototype.toStringWithSourceMap = function SourceNode_toStringWithSourceMap(aArgs) {\n  var generated = {\n    code: \"\",\n    line: 1,\n    column: 0\n  };\n  var map = new SourceMapGenerator(aArgs);\n  var sourceMappingActive = false;\n  var lastOriginalSource = null;\n  var lastOriginalLine = null;\n  var lastOriginalColumn = null;\n  var lastOriginalName = null;\n  this.walk(function (chunk, original) {\n    generated.code += chunk;\n    if (original.source !== null\n        && original.line !== null\n        && original.column !== null) {\n      if(lastOriginalSource !== original.source\n         || lastOriginalLine !== original.line\n         || lastOriginalColumn !== original.column\n         || lastOriginalName !== original.name) {\n        map.addMapping({\n          source: original.source,\n          original: {\n            line: original.line,\n            column: original.column\n          },\n          generated: {\n            line: generated.line,\n            column: generated.column\n          },\n          name: original.name\n        });\n      }\n      lastOriginalSource = original.source;\n      lastOriginalLine = original.line;\n      lastOriginalColumn = original.column;\n      lastOriginalName = original.name;\n      sourceMappingActive = true;\n    } else if (sourceMappingActive) {\n      map.addMapping({\n        generated: {\n          line: generated.line,\n          column: generated.column\n        }\n      });\n      lastOriginalSource = null;\n      sourceMappingActive = false;\n    }\n    for (var idx = 0, length = chunk.length; idx < length; idx++) {\n      if (chunk.charCodeAt(idx) === NEWLINE_CODE) {\n        generated.line++;\n        generated.column = 0;\n        // Mappings end at eol\n        if (idx + 1 === length) {\n          lastOriginalSource = null;\n          sourceMappingActive = false;\n        } else if (sourceMappingActive) {\n          map.addMapping({\n            source: original.source,\n            original: {\n              line: original.line,\n              column: original.column\n            },\n            generated: {\n              line: generated.line,\n              column: generated.column\n            },\n            name: original.name\n          });\n        }\n      } else {\n        generated.column++;\n      }\n    }\n  });\n  this.walkSourceContents(function (sourceFile, sourceContent) {\n    map.setSourceContent(sourceFile, sourceContent);\n  });\n\n  return { code: generated.code, map: map };\n};\n\nexports.SourceNode = SourceNode;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/source-node.js\n// module id = 10\n// module chunks = 0"], "sourceRoot": ""}