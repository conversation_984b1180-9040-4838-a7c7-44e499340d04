var all = require('./helpers').all;

function store(serializeContext, token) {
  var value = typeof token == 'string'
    ? token
    : token[1];
  var wrap = serializeContext.wrap;

  wrap(serializeContext, value);
  track(serializeContext, value);
  serializeContext.output.push(value);
}

function wrap(serializeContext, value) {
  if (serializeContext.column + value.length > serializeContext.format.wrapAt) {
    track(serializeContext, serializeContext.format.breakWith);
    serializeContext.output.push(serializeContext.format.breakWith);
  }
}

function track(serializeContext, value) {
  var parts = value.split('\n');

  serializeContext.line += parts.length - 1;
  serializeContext.column = parts.length > 1 ? 0 : (serializeContext.column + parts.pop().length);
}

function serializeStyles(tokens, context) {
  var serializeContext = {
    column: 0,
    format: context.options.format,
    indentBy: 0,
    indentWith: '',
    line: 1,
    output: [],
    spaceAfterClosingBrace: context.options.compatibility.properties.spaceAfterClosingBrace,
    store: store,
    wrap: context.options.format.wrapAt
      ? wrap
      : function() { /* noop */ }
  };

  all(serializeContext, tokens);

  return { styles: serializeContext.output.join('') };
}

module.exports = serializeStyles;
